import json
import os
import time
import subprocess
import threading
import asyncio
import concurrent.futures
import re
import ast
import shutil
import glob
import urllib.request
import urllib.parse
import queue
import difflib
import tempfile
import zipfile
import tarfile
import sqlite3
import requests
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import hashlib
import pickle
import logging
from collections import defaultdict, deque
import weakref
import gc
import uuid
import signal
import sys
from enum import Enum
import mimetypes
import base64

# Advanced parsing and analysis
try:
    import tree_sitter
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False

# AI Provider imports
import google.generativeai as genai
from dotenv import load_dotenv

# LangChain imports
try:
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.tools import Tool
    from langchain.prompts import PromptTemplate
    from langchain.agents import AgentExecutor, create_react_agent
    from langchain.schema import HumanMessage
    from langchain_community.llms import Ollama
    from langchain_google_genai import ChatGoogleGenerativeAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    print("⚠️ LangChain not available. Some features will be disabled.")
    LANGCHAIN_AVAILABLE = False
    # Create dummy classes to prevent errors
    class ConversationBufferWindowMemory:
        def __init__(self, **kwargs):
            self.buffer = []
    class Tool:
        def __init__(self, **kwargs):
            pass
    class PromptTemplate:
        def __init__(self, **kwargs):
            pass
    class AgentExecutor:
        def __init__(self, **kwargs):
            pass
    def create_react_agent(*args, **kwargs):
        return None
    class HumanMessage:
        def __init__(self, content):
            self.content = content

# Load environment variables
load_dotenv()

# OpenCode-style enums and constants
class ToolResponseType(Enum):
    TEXT = "text"
    IMAGE = "image"

class MessageRole(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"

class FinishReason(Enum):
    STOP = "stop"
    LENGTH = "length"
    TOOL_USE = "tool_use"
    CANCELED = "canceled"

# OpenCode-style tool response structure
@dataclass
class ToolResponse:
    type: ToolResponseType = ToolResponseType.TEXT
    content: str = ""
    metadata: str = ""
    is_error: bool = False

    @classmethod
    def text(cls, content: str) -> 'ToolResponse':
        return cls(type=ToolResponseType.TEXT, content=content)

    @classmethod
    def error(cls, content: str) -> 'ToolResponse':
        return cls(type=ToolResponseType.TEXT, content=content, is_error=True)

@dataclass
class ToolCall:
    id: str
    name: str
    input: str

@dataclass
class ToolInfo:
    name: str
    description: str
    parameters: Dict[str, Any]
    required: List[str]

# OpenCode-style AI Provider Configuration
AI_PROVIDERS = {
    "mistral": {
        "api_keys": [
            "Uy3oDmhY5ZQJR37CP6UYdWFG29x6KiSF",
            "K72lsezTSI5qHKsr4g3H7jyLNRbZ396P",
            "6leEAskSyK3KbzDSaijjlFy70YvhBglQ"
        ],
        "base_url": "https://api.mistral.ai/v1",
        "models": ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest"],
        "context_window": 128000,
        "max_tokens": 8192
    },
    "deepseek": {
        "api_keys": [
            "***********************************",
            "sk-77ff68ee0c634031af8cd4063e0d25ff",
            "sk-f261ad35233149fdbee6eb7ab97853d1"
        ],
        "base_url": "https://api.deepseek.com/v1",
        "models": ["deepseek-chat", "deepseek-coder"],
        "context_window": 64000,
        "max_tokens": 4096
    },
    "gemini": {
        "api_keys": [os.getenv("GEMINI_API_KEY")],
        "models": ["gemini-1.5-flash", "gemini-1.5-flash-8b", "gemini-2.0-flash",
                  "gemini-2.0-flash-001", "gemini-2.0-flash-lite-001", "gemini-2.0-flash-lite"],
        "context_window": 1000000,
        "max_tokens": 8192
    },
    "openai": {
        "api_keys": [os.getenv("OPENAI_API_KEY")],
        "base_url": "https://api.openai.com/v1",
        "models": ["gpt-4o", "gpt-4o-mini", "o1", "o1-mini"],
        "context_window": 128000,
        "max_tokens": 4096
    },
    "anthropic": {
        "api_keys": [os.getenv("ANTHROPIC_API_KEY")],
        "base_url": "https://api.anthropic.com/v1",
        "models": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022"],
        "context_window": 200000,
        "max_tokens": 8192
    }
}

# Base tool interface (OpenCode-style)
class BaseTool:
    def info(self) -> ToolInfo:
        raise NotImplementedError

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        raise NotImplementedError

class AIProviderManager:
    """Manages multiple AI providers with failover and rate limiting"""

    def __init__(self):
        self.current_provider = "mistral"
        self.current_key_index = 0
        self.rate_limits = {}
        self.last_request_time = {}

    def get_current_provider(self):
        """Get current AI provider configuration"""
        return AI_PROVIDERS.get(self.current_provider, AI_PROVIDERS["gemini"])

    def rotate_api_key(self):
        """Rotate to next API key for current provider"""
        provider_config = self.get_current_provider()
        self.current_key_index = (self.current_key_index + 1) % len(provider_config["api_keys"])

    def failover_to_next_provider(self):
        """Failover to next available provider"""
        providers = list(AI_PROVIDERS.keys())
        current_index = providers.index(self.current_provider)
        self.current_provider = providers[(current_index + 1) % len(providers)]
        self.current_key_index = 0

    def make_request(self, prompt: str, max_retries: int = 3) -> str:
        """Make AI request with automatic failover"""
        for attempt in range(max_retries):
            try:
                provider_config = self.get_current_provider()
                api_key = provider_config["api_keys"][self.current_key_index]

                if self.current_provider == "gemini":
                    return self._make_gemini_request(prompt, api_key)
                elif self.current_provider == "mistral":
                    return self._make_mistral_request(prompt, api_key)
                elif self.current_provider == "deepseek":
                    return self._make_deepseek_request(prompt, api_key)
                elif self.current_provider == "openai":
                    return self._make_openai_request(prompt, api_key)
                elif self.current_provider == "anthropic":
                    return self._make_anthropic_request(prompt, api_key)

            except Exception as e:
                print(f"⚠️ Provider {self.current_provider} failed: {e}")
                if attempt < max_retries - 1:
                    self.rotate_api_key()
                    if attempt == 1:  # Try next provider after 2 key failures
                        self.failover_to_next_provider()

        return "❌ All AI providers failed. Please check API keys and connectivity."

    def _make_gemini_request(self, prompt: str, api_key: str) -> str:
        """Make request to Gemini API"""
        try:
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-2.0-flash')
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            raise Exception(f"Gemini API error: {e}")

    def _make_mistral_request(self, prompt: str, api_key: str) -> str:
        """Make request to Mistral API"""
        try:
            import requests
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": "mistral-large-latest",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1
            }
            response = requests.post(
                "https://api.mistral.ai/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            raise Exception(f"Mistral API error: {e}")

    def _make_deepseek_request(self, prompt: str, api_key: str) -> str:
        """Make request to DeepSeek API"""
        try:
            import requests
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1
            }
            response = requests.post(
                "https://api.deepseek.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            raise Exception(f"DeepSeek API error: {e}")

    def _make_openai_request(self, prompt: str, api_key: str) -> str:
        """Make request to OpenAI API"""
        try:
            import requests
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": "gpt-4o",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1
            }
            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            raise Exception(f"OpenAI API error: {e}")

    def _make_anthropic_request(self, prompt: str, api_key: str) -> str:
        """Make request to Anthropic API"""
        try:
            import requests
            headers = {
                "x-api-key": api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            data = {
                "model": "claude-3-5-sonnet-20241022",
                "max_tokens": 4096,
                "messages": [{"role": "user", "content": prompt}]
            }
            response = requests.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()["content"][0]["text"]
        except Exception as e:
            raise Exception(f"Anthropic API error: {e}")

    def switch_provider(self, provider_name: str) -> str:
        """Switch to a different AI provider"""
        if provider_name.lower() not in AI_PROVIDERS:
            available = ", ".join(AI_PROVIDERS.keys())
            return f"❌ Provider '{provider_name}' not available. Available providers: {available}"

        old_provider = self.current_provider
        self.current_provider = provider_name.lower()
        self.current_key_index = 0

        # Test the new provider
        try:
            test_response = self.make_request("Hello", max_retries=1)
            if "❌" in test_response:
                # Revert if test fails
                self.current_provider = old_provider
                return f"❌ Failed to switch to {provider_name}: {test_response}"

            return f"✅ Successfully switched to {provider_name.title()} provider"
        except Exception as e:
            # Revert if test fails
            self.current_provider = old_provider
            return f"❌ Failed to switch to {provider_name}: {str(e)}"

    def get_provider_status(self) -> str:
        """Get status of all providers"""
        status_lines = [f"🤖 AI Provider Status:"]

        for provider_name, config in AI_PROVIDERS.items():
            current_marker = "👉 " if provider_name == self.current_provider else "   "
            api_keys_count = len([k for k in config["api_keys"] if k])
            context_window = config.get("context_window", "Unknown")
            max_tokens = config.get("max_tokens", "Unknown")

            status_lines.append(
                f"{current_marker}{provider_name.title()}: "
                f"{api_keys_count} API keys, "
                f"Context: {context_window:,}, "
                f"Max tokens: {max_tokens:,}"
            )

        return "\n".join(status_lines)

# OpenCode-style Tools Implementation
class ViewTool(BaseTool):
    """Enhanced file viewing tool with line numbers and LSP integration"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="view",
            description="""File viewing tool that reads and displays the contents of files with line numbers, allowing you to examine code, logs, or text data.

WHEN TO USE THIS TOOL:
- Use when you need to read the contents of a specific file
- Helpful for examining source code, configuration files, or log files
- Perfect for looking at text-based file formats

HOW TO USE:
- Provide the path to the file you want to view
- Optionally specify an offset to start reading from a specific line
- Optionally specify a limit to control how many lines are read

FEATURES:
- Displays file contents with line numbers for easy reference
- Can read from any position in a file using the offset parameter
- Handles large files by limiting the number of lines read
- Automatically truncates very long lines for better display
- Suggests similar file names when the requested file isn't found

LIMITATIONS:
- Maximum file size is 250KB
- Default reading limit is 2000 lines
- Lines longer than 2000 characters are truncated
- Cannot display binary files or images

TIPS:
- Use with Glob tool to first find files you want to view
- For code exploration, first use Grep to find relevant files, then View to examine them
- When viewing large files, use the offset parameter to read specific sections""",
            parameters={
                "file_path": {
                    "type": "string",
                    "description": "The path to the file to read"
                },
                "offset": {
                    "type": "integer",
                    "description": "The line number to start reading from (0-based)"
                },
                "limit": {
                    "type": "integer",
                    "description": "The number of lines to read (defaults to 2000)"
                }
            },
            required=["file_path"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            file_path = call_params.get("file_path", "")
            offset = call_params.get("offset", 0)
            limit = call_params.get("limit", 2000)

            if not file_path:
                return ToolResponse.error("file_path is required")

            # Handle relative paths
            if not os.path.isabs(file_path):
                file_path = os.path.join(os.getcwd(), file_path)

            # Check if file exists
            if not os.path.exists(file_path):
                # Try to offer suggestions for similarly named files
                dir_path = os.path.dirname(file_path)
                base_name = os.path.basename(file_path)

                if os.path.exists(dir_path):
                    suggestions = []
                    for entry in os.listdir(dir_path):
                        if (base_name.lower() in entry.lower() or
                            entry.lower() in base_name.lower()):
                            suggestions.append(os.path.join(dir_path, entry))
                            if len(suggestions) >= 3:
                                break

                    if suggestions:
                        return ToolResponse.error(
                            f"File not found: {file_path}\n\n"
                            f"Did you mean one of these?\n" +
                            "\n".join(suggestions)
                        )

                return ToolResponse.error(f"File not found: {file_path}")

            # Check if it's a directory
            if os.path.isdir(file_path):
                return ToolResponse.error(f"Path is a directory, not a file: {file_path}")

            # Check file size (250KB limit)
            file_size = os.path.getsize(file_path)
            if file_size > 250 * 1024:
                return ToolResponse.error(
                    f"File is too large ({file_size} bytes). Maximum size is 250KB"
                )

            # Check if it's an image file
            if self._is_image_file(file_path):
                image_type = self._get_image_type(file_path)
                return ToolResponse.error(
                    f"This is an image file of type: {image_type}\n"
                    f"Use a different tool to process images"
                )

            # Read the file content
            content, total_lines = self._read_text_file(file_path, offset, limit)

            # Format output with line numbers
            output = "\n" + self._add_line_numbers(content, offset + 1)

            # Add truncation note if needed
            content_lines = len(content.split('\n'))
            if total_lines > offset + content_lines:
                output += f"\n\n(File has more lines. Use 'offset' parameter to read beyond line {offset + content_lines})"

            return ToolResponse.text(output)

        except Exception as e:
            return ToolResponse.error(f"Error reading file: {str(e)}")

    def _is_image_file(self, file_path: str) -> bool:
        """Check if file is an image"""
        ext = os.path.splitext(file_path)[1].lower()
        return ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp']

    def _get_image_type(self, file_path: str) -> str:
        """Get image type from extension"""
        ext = os.path.splitext(file_path)[1].lower()
        type_map = {
            '.jpg': 'JPEG', '.jpeg': 'JPEG', '.png': 'PNG',
            '.gif': 'GIF', '.bmp': 'BMP', '.svg': 'SVG', '.webp': 'WebP'
        }
        return type_map.get(ext, 'Unknown')

    def _read_text_file(self, file_path: str, offset: int, limit: int) -> Tuple[str, int]:
        """Read text file with offset and limit"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            total_lines = len(lines)

            # Apply offset and limit
            start_idx = max(0, offset)
            end_idx = min(len(lines), start_idx + limit)
            selected_lines = lines[start_idx:end_idx]

            # Truncate very long lines
            processed_lines = []
            for line in selected_lines:
                line = line.rstrip('\n\r')
                if len(line) > 2000:
                    line = line[:2000] + "..."
                processed_lines.append(line)

            return '\n'.join(processed_lines), total_lines

    def _add_line_numbers(self, content: str, start_line: int) -> str:
        """Add line numbers to content"""
        if not content:
            return ""

        lines = content.split('\n')
        result = []

        for i, line in enumerate(lines):
            line_num = i + start_line
            num_str = str(line_num)

            if len(num_str) >= 6:
                result.append(f"{num_str}|{line}")
            else:
                padded_num = f"{num_str:>6}"
                result.append(f"{padded_num}|{line}")

        return '\n'.join(result)

class EditTool(BaseTool):
    """Advanced file editing tool with diff support"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="edit",
            description="""Advanced file editing tool that allows you to make precise changes to files with diff support and validation.

FEATURES:
- Create new files or edit existing ones
- Apply changes with automatic backup
- Show diffs of changes made
- Validate changes before applying
- Support for various editing operations

PARAMETERS:
- file_path: Path to the file to edit
- content: New content for the file
- operation: Type of operation (create, replace, append, prepend)
- backup: Whether to create backup (default: true)""",
            parameters={
                "file_path": {
                    "type": "string",
                    "description": "Path to the file to edit"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write to the file"
                },
                "operation": {
                    "type": "string",
                    "description": "Operation type: create, replace, append, prepend"
                },
                "backup": {
                    "type": "boolean",
                    "description": "Create backup before editing (default: true)"
                }
            },
            required=["file_path", "content"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            file_path = call_params.get("file_path", "")
            content = call_params.get("content", "")
            operation = call_params.get("operation", "replace")
            backup = call_params.get("backup", True)

            if not file_path:
                return ToolResponse.error("file_path is required")

            # Handle relative paths
            if not os.path.isabs(file_path):
                file_path = os.path.join(os.getcwd(), file_path)

            # Ensure directory exists
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            original_content = ""
            file_exists = os.path.exists(file_path)

            # Read original content if file exists
            if file_exists:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        original_content = f.read()
                except Exception as e:
                    return ToolResponse.error(f"Error reading existing file: {str(e)}")

            # Create backup if requested and file exists
            if backup and file_exists:
                backup_path = f"{file_path}.backup_{int(time.time())}"
                try:
                    shutil.copy2(file_path, backup_path)
                except Exception as e:
                    return ToolResponse.error(f"Error creating backup: {str(e)}")

            # Determine new content based on operation
            if operation == "create":
                if file_exists:
                    return ToolResponse.error(f"File already exists: {file_path}")
                new_content = content
            elif operation == "replace":
                new_content = content
            elif operation == "append":
                new_content = original_content + content
            elif operation == "prepend":
                new_content = content + original_content
            else:
                return ToolResponse.error(f"Unknown operation: {operation}")

            # Write the new content
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
            except Exception as e:
                return ToolResponse.error(f"Error writing file: {str(e)}")

            # Validate the write
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    written_content = f.read()

                if written_content != new_content:
                    return ToolResponse.error("File written but content validation failed")
            except Exception as e:
                return ToolResponse.error(f"Error validating written content: {str(e)}")

            # Generate diff if file was modified
            diff_output = ""
            if file_exists and original_content != new_content:
                diff_lines = list(difflib.unified_diff(
                    original_content.splitlines(keepends=True),
                    new_content.splitlines(keepends=True),
                    fromfile=f"{file_path} (original)",
                    tofile=f"{file_path} (modified)",
                    lineterm=""
                ))
                if diff_lines:
                    diff_output = "\n\nChanges made:\n" + "".join(diff_lines)

            # Prepare response
            if operation == "create":
                response = f"✅ File created: {file_path} ({len(new_content)} chars)"
            else:
                response = f"✅ File edited: {file_path} ({len(new_content)} chars)"

            if backup and file_exists:
                response += f"\n📁 Backup created: {backup_path}"

            response += diff_output

            return ToolResponse.text(response)

        except Exception as e:
            return ToolResponse.error(f"Error editing file: {str(e)}")

class WriteTool(BaseTool):
    """Simple file writing tool (OpenCode compatible)"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="write",
            description="Write content to a file. Creates the file if it doesn't exist, overwrites if it does.",
            parameters={
                "file_path": {
                    "type": "string",
                    "description": "Path to the file to write"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write to the file"
                }
            },
            required=["file_path", "content"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            file_path = call_params.get("file_path", "")
            content = call_params.get("content", "")

            if not file_path:
                return ToolResponse.error("file_path is required")

            # Handle relative paths
            if not os.path.isabs(file_path):
                file_path = os.path.join(os.getcwd(), file_path)

            # Ensure directory exists
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return ToolResponse.text(f"✅ File written: {file_path} ({len(content)} chars)")

        except Exception as e:
            return ToolResponse.error(f"Error writing file: {str(e)}")

class BashTool(BaseTool):
    """Enhanced shell execution tool with security controls"""

    # Banned commands for security
    BANNED_COMMANDS = [
        "alias", "curl", "curlie", "wget", "axel", "aria2c", "nc", "telnet",
        "lynx", "w3m", "links", "httpie", "xh", "http-prompt", "chrome",
        "firefox", "safari", "rm -rf", "format", "fdisk", "mkfs"
    ]

    # Safe read-only commands that don't need permission
    SAFE_COMMANDS = [
        "ls", "echo", "pwd", "date", "cal", "uptime", "whoami", "id", "groups",
        "env", "printenv", "which", "type", "whereis", "whatis", "uname",
        "hostname", "df", "du", "free", "top", "ps", "git status", "git log",
        "git diff", "git show", "git branch", "go version", "node --version"
    ]

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="bash",
            description=f"""Executes a given bash command with security measures and proper handling.

SECURITY FEATURES:
- Banned commands are blocked for security: {', '.join(self.BANNED_COMMANDS[:10])}...
- Safe read-only commands execute without permission prompts
- Persistent shell session maintains state between commands
- Output is truncated if it exceeds 30,000 characters

USAGE NOTES:
- Commands share the same shell session (environment variables persist)
- Use ';' or '&&' to separate multiple commands in one call
- Avoid using 'find' and 'grep' - use dedicated Grep and Glob tools instead
- Avoid 'cat', 'head', 'tail' - use View tool for reading files
- Try to maintain current working directory with absolute paths

TIMEOUT:
- Default timeout: 60 seconds
- Maximum timeout: 600 seconds (10 minutes)

IMPORTANT: This tool maintains shell state between calls. Environment variables,
virtual environments, and current directory persist across commands.""",
            parameters={
                "command": {
                    "type": "string",
                    "description": "The command to execute"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Optional timeout in seconds (max 600)"
                }
            },
            required=["command"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            command = call_params.get("command", "")
            timeout = call_params.get("timeout", 60)

            if not command:
                return ToolResponse.error("command is required")

            # Validate timeout
            if timeout > 600:
                timeout = 600
            elif timeout <= 0:
                timeout = 60

            # Check for banned commands
            base_cmd = command.split()[0] if command.split() else ""
            for banned in self.BANNED_COMMANDS:
                if base_cmd.lower() == banned.lower() or banned.lower() in command.lower():
                    return ToolResponse.error(f"Command '{banned}' is not allowed for security reasons")

            # Check if it's a safe command (no permission needed)
            is_safe = False
            cmd_lower = command.lower().strip()
            for safe in self.SAFE_COMMANDS:
                if cmd_lower.startswith(safe.lower()):
                    if len(cmd_lower) == len(safe) or cmd_lower[len(safe)] in [' ', '-']:
                        is_safe = True
                        break

            # For non-safe commands, we would normally ask for permission
            # For this implementation, we'll proceed but log the action
            if not is_safe:
                print(f"🔒 Executing potentially unsafe command: {command}")

            start_time = time.time()

            # Execute the command
            try:
                if os.name == 'nt':  # Windows
                    result = subprocess.run(
                        ['powershell', '-Command', command],
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        cwd=os.getcwd()
                    )
                else:  # Unix-like
                    result = subprocess.run(
                        command,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=timeout,
                        cwd=os.getcwd()
                    )

                stdout = result.stdout or ""
                stderr = result.stderr or ""
                exit_code = result.returncode

                # Truncate output if too long
                stdout = self._truncate_output(stdout)
                stderr = self._truncate_output(stderr)

                # Prepare response
                output_parts = []
                if stdout:
                    output_parts.append(stdout)

                if stderr:
                    if stdout:
                        output_parts.append("\n")
                    output_parts.append(f"STDERR:\n{stderr}")

                if exit_code != 0:
                    if output_parts:
                        output_parts.append("\n")
                    output_parts.append(f"Exit code: {exit_code}")

                execution_time = time.time() - start_time

                if not output_parts:
                    response = f"✅ Command completed successfully (no output) in {execution_time:.2f}s"
                else:
                    response = "".join(output_parts)
                    if execution_time > 1:
                        response += f"\n\n⏱️ Execution time: {execution_time:.2f}s"

                return ToolResponse.text(response)

            except subprocess.TimeoutExpired:
                return ToolResponse.error(f"Command timed out after {timeout} seconds")

        except Exception as e:
            return ToolResponse.error(f"Error executing command: {str(e)}")

    def _truncate_output(self, content: str, max_length: int = 30000) -> str:
        """Truncate output if it's too long"""
        if len(content) <= max_length:
            return content

        half_length = max_length // 2
        start = content[:half_length]
        end = content[-half_length:]
        truncated_lines = content[half_length:-half_length].count('\n')

        return f"{start}\n\n... [{truncated_lines} lines truncated] ...\n\n{end}"

class GlobTool(BaseTool):
    """Pattern-based file finding tool"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="glob",
            description="""Find files by pattern using glob syntax.

FEATURES:
- Support for standard glob patterns (*, ?, [], {})
- Recursive search with **
- Filter by file types and patterns
- Exclude hidden files by default

EXAMPLES:
- "*.py" - Find all Python files in current directory
- "**/*.js" - Find all JavaScript files recursively
- "src/**/*.{py,js}" - Find Python and JS files in src directory
- "test_*.py" - Find test files starting with "test_"

PARAMETERS:
- pattern: Glob pattern to search for
- path: Directory to search in (default: current directory)""",
            parameters={
                "pattern": {
                    "type": "string",
                    "description": "Glob pattern to search for"
                },
                "path": {
                    "type": "string",
                    "description": "Directory to search in (default: current directory)"
                }
            },
            required=["pattern"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            pattern = call_params.get("pattern", "")
            search_path = call_params.get("path", ".")

            if not pattern:
                return ToolResponse.error("pattern is required")

            # Handle relative paths
            if not os.path.isabs(search_path):
                search_path = os.path.join(os.getcwd(), search_path)

            if not os.path.exists(search_path):
                return ToolResponse.error(f"Search path does not exist: {search_path}")

            # Perform glob search
            search_pattern = os.path.join(search_path, pattern)
            matches = glob.glob(search_pattern, recursive=True)

            # Filter out hidden files and directories
            filtered_matches = []
            for match in matches:
                # Skip hidden files/directories (starting with .)
                path_parts = Path(match).parts
                if not any(part.startswith('.') for part in path_parts):
                    filtered_matches.append(match)

            if not filtered_matches:
                return ToolResponse.text(f"No files found matching pattern: {pattern}")

            # Sort matches
            filtered_matches.sort()

            # Prepare output
            output_lines = [f"Found {len(filtered_matches)} files matching '{pattern}':"]

            for match in filtered_matches[:100]:  # Limit to 100 results
                # Get relative path for cleaner display
                try:
                    rel_path = os.path.relpath(match, search_path)
                    if os.path.isfile(match):
                        file_size = os.path.getsize(match)
                        output_lines.append(f"📄 {rel_path} ({file_size} bytes)")
                    else:
                        output_lines.append(f"📁 {rel_path}/")
                except:
                    output_lines.append(f"📄 {match}")

            if len(filtered_matches) > 100:
                output_lines.append(f"\n... and {len(filtered_matches) - 100} more files")

            return ToolResponse.text("\n".join(output_lines))

        except Exception as e:
            return ToolResponse.error(f"Error searching files: {str(e)}")

class GrepTool(BaseTool):
    """Advanced content searching tool"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="grep",
            description="""Search file contents using patterns with advanced filtering.

FEATURES:
- Regular expression and literal text search
- Search in specific file types
- Include/exclude patterns for files
- Context lines around matches
- Case-sensitive and case-insensitive search

PARAMETERS:
- pattern: Search pattern (regex or literal text)
- path: Directory to search in (default: current directory)
- include: File patterns to include (e.g., "*.py,*.js")
- literal_text: If true, treat pattern as literal text (not regex)
- context: Number of context lines to show around matches""",
            parameters={
                "pattern": {
                    "type": "string",
                    "description": "Pattern to search for"
                },
                "path": {
                    "type": "string",
                    "description": "Directory to search in (default: current directory)"
                },
                "include": {
                    "type": "string",
                    "description": "Comma-separated file patterns to include (e.g., '*.py,*.js')"
                },
                "literal_text": {
                    "type": "boolean",
                    "description": "Treat pattern as literal text, not regex"
                },
                "context": {
                    "type": "integer",
                    "description": "Number of context lines around matches (default: 2)"
                }
            },
            required=["pattern"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            pattern = call_params.get("pattern", "")
            search_path = call_params.get("path", ".")
            include_patterns = call_params.get("include", "")
            literal_text = call_params.get("literal_text", False)
            context_lines = call_params.get("context", 2)

            if not pattern:
                return ToolResponse.error("pattern is required")

            # Handle relative paths
            if not os.path.isabs(search_path):
                search_path = os.path.join(os.getcwd(), search_path)

            if not os.path.exists(search_path):
                return ToolResponse.error(f"Search path does not exist: {search_path}")

            # Parse include patterns
            if include_patterns:
                file_patterns = [p.strip() for p in include_patterns.split(',')]
            else:
                file_patterns = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json",
                               "*.md", "*.txt", "*.yml", "*.yaml", "*.sh", "*.bash"]

            # Compile regex pattern
            if literal_text:
                regex_pattern = re.escape(pattern)
            else:
                try:
                    regex_pattern = pattern
                    re.compile(regex_pattern)  # Test if valid regex
                except re.error as e:
                    return ToolResponse.error(f"Invalid regex pattern: {str(e)}")

            # Find files to search
            files_to_search = []
            for file_pattern in file_patterns:
                search_glob = os.path.join(search_path, "**", file_pattern)
                files_to_search.extend(glob.glob(search_glob, recursive=True))

            # Remove duplicates and filter
            files_to_search = list(set(files_to_search))
            files_to_search = [f for f in files_to_search if os.path.isfile(f)]

            matches = []
            files_searched = 0

            for file_path in files_to_search[:1000]:  # Limit to 1000 files
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        files_searched += 1

                        for line_num, line in enumerate(lines, 1):
                            if re.search(regex_pattern, line, re.IGNORECASE):
                                # Get context lines
                                start_line = max(0, line_num - context_lines - 1)
                                end_line = min(len(lines), line_num + context_lines)

                                context = []
                                for i in range(start_line, end_line):
                                    prefix = ">" if i == line_num - 1 else " "
                                    context.append(f"{prefix}{i+1:4d}: {lines[i].rstrip()}")

                                matches.append({
                                    'file': file_path,
                                    'line': line_num,
                                    'context': context
                                })

                                if len(matches) >= 50:  # Limit matches
                                    break

                    if len(matches) >= 50:
                        break

                except (UnicodeDecodeError, PermissionError):
                    continue

            if not matches:
                return ToolResponse.text(
                    f"No matches found for '{pattern}' in {files_searched} files"
                )

            # Format output
            output_lines = [
                f"Found {len(matches)} matches for '{pattern}' in {files_searched} files:"
            ]

            current_file = None
            for match in matches:
                if match['file'] != current_file:
                    current_file = match['file']
                    rel_path = os.path.relpath(match['file'], search_path)
                    output_lines.append(f"\n📄 {rel_path}:")

                output_lines.append("\n".join(match['context']))
                output_lines.append("")

            if len(matches) >= 50:
                output_lines.append("... (showing first 50 matches)")

            return ToolResponse.text("\n".join(output_lines))

        except Exception as e:
            return ToolResponse.error(f"Error searching content: {str(e)}")

class LsTool(BaseTool):
    """Enhanced directory listing tool"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="ls",
            description="""List directory contents with detailed information.

FEATURES:
- Show files and directories with sizes and permissions
- Filter by patterns
- Show hidden files optionally
- Recursive listing support
- Sort by name, size, or modification time

PARAMETERS:
- path: Directory to list (default: current directory)
- ignore: Patterns to ignore (comma-separated)
- show_hidden: Include hidden files (default: false)
- recursive: Show subdirectories recursively (default: false)""",
            parameters={
                "path": {
                    "type": "string",
                    "description": "Directory path to list (default: current directory)"
                },
                "ignore": {
                    "type": "array",
                    "description": "Patterns to ignore"
                },
                "show_hidden": {
                    "type": "boolean",
                    "description": "Include hidden files (default: false)"
                },
                "recursive": {
                    "type": "boolean",
                    "description": "List subdirectories recursively (default: false)"
                }
            },
            required=[]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            list_path = call_params.get("path", ".")
            ignore_patterns = call_params.get("ignore", [])
            show_hidden = call_params.get("show_hidden", False)
            recursive = call_params.get("recursive", False)

            # Handle relative paths
            if not os.path.isabs(list_path):
                list_path = os.path.join(os.getcwd(), list_path)

            if not os.path.exists(list_path):
                return ToolResponse.error(f"Path does not exist: {list_path}")

            if not os.path.isdir(list_path):
                return ToolResponse.error(f"Path is not a directory: {list_path}")

            entries = []

            if recursive:
                for root, dirs, files in os.walk(list_path):
                    # Filter hidden directories if needed
                    if not show_hidden:
                        dirs[:] = [d for d in dirs if not d.startswith('.')]

                    for name in dirs + files:
                        if not show_hidden and name.startswith('.'):
                            continue

                        # Check ignore patterns
                        if self._should_ignore(name, ignore_patterns):
                            continue

                        full_path = os.path.join(root, name)
                        rel_path = os.path.relpath(full_path, list_path)
                        entries.append(self._get_entry_info(full_path, rel_path))
            else:
                try:
                    for name in os.listdir(list_path):
                        if not show_hidden and name.startswith('.'):
                            continue

                        # Check ignore patterns
                        if self._should_ignore(name, ignore_patterns):
                            continue

                        full_path = os.path.join(list_path, name)
                        entries.append(self._get_entry_info(full_path, name))
                except PermissionError:
                    return ToolResponse.error(f"Permission denied accessing: {list_path}")

            if not entries:
                return ToolResponse.text("Directory is empty or all files are filtered out")

            # Sort entries (directories first, then files)
            entries.sort(key=lambda x: (not x['is_dir'], x['name'].lower()))

            # Format output
            output_lines = [f"📁 Directory listing for: {list_path}"]
            output_lines.append("")

            for entry in entries[:200]:  # Limit to 200 entries
                icon = "📁" if entry['is_dir'] else "📄"
                size_str = f"{entry['size']:>8}" if not entry['is_dir'] else "     DIR"
                mod_time = entry['modified'].strftime("%Y-%m-%d %H:%M")

                output_lines.append(f"{icon} {size_str} {mod_time} {entry['name']}")

            if len(entries) > 200:
                output_lines.append(f"\n... and {len(entries) - 200} more entries")

            return ToolResponse.text("\n".join(output_lines))

        except Exception as e:
            return ToolResponse.error(f"Error listing directory: {str(e)}")

    def _should_ignore(self, name: str, ignore_patterns: List[str]) -> bool:
        """Check if file should be ignored based on patterns"""
        for pattern in ignore_patterns:
            if re.match(pattern.replace('*', '.*'), name):
                return True
        return False

    def _get_entry_info(self, full_path: str, display_name: str) -> Dict[str, Any]:
        """Get information about a file/directory entry"""
        try:
            stat = os.stat(full_path)
            return {
                'name': display_name,
                'is_dir': os.path.isdir(full_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime)
            }
        except:
            return {
                'name': display_name,
                'is_dir': False,
                'size': 0,
                'modified': datetime.now()
            }

class FetchTool(BaseTool):
    """Web content fetching tool"""

    def info(self) -> ToolInfo:
        return ToolInfo(
            name="fetch",
            description="""Fetch data from URLs and return the content.

FEATURES:
- Fetch web pages, APIs, and other HTTP resources
- Support for different content types (text, JSON, HTML)
- Configurable timeout
- Basic error handling and status codes

PARAMETERS:
- url: URL to fetch
- format: Expected format (text, json, html)
- timeout: Request timeout in seconds (default: 10)""",
            parameters={
                "url": {
                    "type": "string",
                    "description": "URL to fetch"
                },
                "format": {
                    "type": "string",
                    "description": "Expected format: text, json, html"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Request timeout in seconds (default: 10)"
                }
            },
            required=["url", "format"]
        )

    def run(self, ctx: Dict[str, Any], params: ToolCall) -> ToolResponse:
        try:
            call_params = json.loads(params.input)
            url = call_params.get("url", "")
            format_type = call_params.get("format", "text")
            timeout = call_params.get("timeout", 10)

            if not url:
                return ToolResponse.error("url is required")

            if format_type not in ["text", "json", "html"]:
                return ToolResponse.error("format must be one of: text, json, html")

            # Validate URL
            if not url.startswith(('http://', 'https://')):
                return ToolResponse.error("URL must start with http:// or https://")

            # Make the request
            try:
                response = requests.get(url, timeout=timeout)
                response.raise_for_status()

                content = response.text
                status_code = response.status_code
                content_type = response.headers.get('content-type', '')

                # Process based on format
                if format_type == "json":
                    try:
                        json_data = response.json()
                        content = json.dumps(json_data, indent=2)
                    except json.JSONDecodeError:
                        return ToolResponse.error("Response is not valid JSON")

                elif format_type == "html":
                    # Basic HTML cleaning for better readability
                    content = re.sub(r'<script[^>]*>.*?</script>', '', content, flags=re.DOTALL)
                    content = re.sub(r'<style[^>]*>.*?</style>', '', content, flags=re.DOTALL)
                    content = re.sub(r'<[^>]+>', '', content)
                    content = re.sub(r'\s+', ' ', content).strip()

                # Truncate if too long
                if len(content) > 10000:
                    content = content[:10000] + "\n\n... (content truncated)"

                result = f"✅ Fetched from {url}\n"
                result += f"Status: {status_code}\n"
                result += f"Content-Type: {content_type}\n"
                result += f"Size: {len(response.content)} bytes\n\n"
                result += content

                return ToolResponse.text(result)

            except requests.exceptions.Timeout:
                return ToolResponse.error(f"Request timed out after {timeout} seconds")
            except requests.exceptions.ConnectionError:
                return ToolResponse.error("Connection error - could not reach the URL")
            except requests.exceptions.HTTPError as e:
                return ToolResponse.error(f"HTTP error: {e}")
            except requests.exceptions.RequestException as e:
                return ToolResponse.error(f"Request error: {e}")

        except Exception as e:
            return ToolResponse.error(f"Error fetching URL: {str(e)}")

# Initialize AI Provider Manager
ai_provider = AIProviderManager()
print(f"✅ AI Provider Manager initialized with {ai_provider.current_provider}")

# Advanced Context and State Management
@dataclass
class PredictiveCache:
    suggestions: Dict[str, List[str]] = field(default_factory=dict)
    code_snippets: Dict[str, str] = field(default_factory=dict)
    next_actions: List[str] = field(default_factory=list)
    context_patterns: Dict[str, int] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class CodeAnalysisResult:
    complexity: int = 0
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    duplicates: List[Dict] = field(default_factory=list)
    security_issues: List[str] = field(default_factory=list)
    performance_issues: List[str] = field(default_factory=list)
    refactor_suggestions: List[str] = field(default_factory=list)

@dataclass
class AgentContext:
    current_directory: str = os.getcwd()
    active_files: List[str] = field(default_factory=list)
    command_history: List[str] = field(default_factory=list)
    project_structure: Dict = field(default_factory=dict)
    last_error: str = ""
    working_memory: Dict = field(default_factory=dict)
    predictive_cache: PredictiveCache = field(default_factory=PredictiveCache)
    code_analysis: Dict[str, CodeAnalysisResult] = field(default_factory=dict)
    language_preferences: Dict[str, str] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    git_status: Dict = field(default_factory=dict)

    def __post_init__(self):
        if not self.active_files:
            self.active_files = []
        if not self.command_history:
            self.command_history = []
        if not self.working_memory:
            self.working_memory = {}

class PredictivePrefetcher:
    def __init__(self, agent_context):
        self.context = agent_context
        self.prediction_queue = queue.Queue()
        self.suggestion_cache = {}
        self.pattern_analyzer = PatternAnalyzer()
        self.is_running = False

    def start_background_prediction(self):
        """Start background prediction thread"""
        if not self.is_running:
            self.is_running = True
            threading.Thread(target=self._prediction_worker, daemon=True).start()

    def _prediction_worker(self):
        """Background worker for predictive prefetching"""
        while self.is_running:
            try:
                # Analyze current context and predict next actions
                predictions = self._generate_predictions()
                self.context.predictive_cache.next_actions = predictions
                time.sleep(2)  # Update every 2 seconds
            except Exception as e:
                logging.error(f"Prediction worker error: {e}")

    def _generate_predictions(self):
        """Generate predictions based on current context"""
        predictions = []

        # Analyze command history patterns
        if len(self.context.command_history) >= 2:
            last_commands = self.context.command_history[-3:]
            patterns = self.pattern_analyzer.analyze_command_patterns(last_commands)
            predictions.extend(patterns)

        # Analyze file context
        if self.context.active_files:
            file_predictions = self.pattern_analyzer.analyze_file_patterns(self.context.active_files)
            predictions.extend(file_predictions)

        return predictions[:10]  # Top 10 predictions

class PatternAnalyzer:
    def __init__(self):
        self.command_patterns = {
            ('git', 'add'): ['git commit -m "Update"', 'git push'],
            ('npm', 'install'): ['npm start', 'npm run dev', 'npm test'],
            ('pip', 'install'): ['python -m pytest', 'python main.py'],
            ('create', 'file'): ['edit file', 'run file', 'test file'],
            ('write', 'code'): ['run code', 'test code', 'debug code']
        }

    def analyze_command_patterns(self, commands):
        """Analyze command patterns and suggest next actions"""
        suggestions = []
        for i in range(len(commands) - 1):
            pattern = tuple(commands[i].split()[:2])
            if pattern in self.command_patterns:
                suggestions.extend(self.command_patterns[pattern])
        return suggestions

    def analyze_file_patterns(self, files):
        """Analyze file patterns and suggest actions"""
        suggestions = []
        for file_path in files:
            ext = Path(file_path).suffix.lower()
            if ext == '.py':
                suggestions.extend(['run python file', 'test python code', 'lint python code'])
            elif ext in ['.js', '.ts']:
                suggestions.extend(['run node file', 'test javascript', 'build project'])
            elif ext == '.html':
                suggestions.extend(['open in browser', 'validate html', 'test responsive'])
                
        return suggestions

class AdvancedCodingAgent:
    def __init__(self):
        self.context = AgentContext()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=8)

        # Initialize memory with fallback
        if LANGCHAIN_AVAILABLE:
            self.memory = ConversationBufferWindowMemory(k=15, return_messages=True)
        else:
            self.memory = ConversationBufferWindowMemory(k=15, return_messages=True)

        self.cache = {}
        self.running_processes = {}
        self.predictive_prefetcher = PredictivePrefetcher(self.context)

        # Initialize OpenCode-style tools
        self.opencode_tools = {
            'view': ViewTool(),
            'edit': EditTool(),
            'write': WriteTool(),
            'bash': BashTool(),
            'glob': GlobTool(),
            'grep': GrepTool(),
            'ls': LsTool(),
            'fetch': FetchTool()
        }

        # Legacy components (preserved for compatibility)
        self.code_analyzer = AdvancedCodeAnalyzer()
        self.language_converter = LanguageConverter()
        self.refactoring_engine = RefactoringEngine()
        self.web_scraper = EnhancedWebScraper()
        self.git_manager = GitManager()
        self.package_manager = PackageManager()

        # Start background services
        self.predictive_prefetcher.start_background_prediction()
        self._setup_logging()

    def _setup_logging(self):
        """Setup logging for the agent"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('agent.log'),
                logging.StreamHandler()
            ]
        )

    def run_command(self, command: str, timeout: int = 30) -> str:
        """Execute PowerShell commands with advanced error handling"""
        try:
            self.context.command_history.append(command)

            # Use PowerShell for Windows
            if os.name == 'nt':
                cmd = ['powershell', '-Command', command]
            else:
                cmd = command

            result = subprocess.run(
                cmd,
                shell=True if os.name != 'nt' else False,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.context.current_directory
            )

            output = result.stdout.strip() if result.stdout else ""
            error = result.stderr.strip() if result.stderr else ""

            if result.returncode == 0:
                return f"✅ Command executed successfully:\n{output}"
            else:
                self.context.last_error = error
                return f"❌ Command failed (code {result.returncode}):\n{error}\nOutput: {output}"

        except subprocess.TimeoutExpired:
            return f"⏰ Command timed out after {timeout} seconds"
        except Exception as e:
            error_msg = f"❌ Error executing command: {str(e)}"
            self.context.last_error = error_msg
            return error_msg

    def write_file(self, path: str, content: str, backup: bool = True) -> str:
        """Advanced file writing with backup and validation"""
        try:
            abs_path = os.path.abspath(path)
            dir_path = os.path.dirname(abs_path)

            # Create backup if file exists
            if backup and os.path.exists(abs_path):
                backup_path = f"{abs_path}.backup_{int(time.time())}"
                shutil.copy2(abs_path, backup_path)

            # Ensure directory exists
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write file with encoding
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            # Validate written content
            with open(abs_path, 'r', encoding='utf-8') as f:
                written_content = f.read()

            if written_content == content:
                return f"✅ File '{path}' written successfully ({len(content)} chars)"
            else:
                return f"⚠️ File written but content validation failed"

        except Exception as e:
            return f"❌ Error writing file '{path}': {str(e)}"

    def read_file(self, file_path: str, lines: Optional[Tuple[int, int]] = None) -> str:
        """Advanced file reading with line range support"""
        try:
            abs_path = os.path.abspath(file_path)

            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"

            with open(abs_path, 'r', encoding='utf-8') as f:
                if lines:
                    all_lines = f.readlines()
                    start, end = lines
                    selected_lines = all_lines[start-1:end] if end != -1 else all_lines[start-1:]
                    content = ''.join(selected_lines)
                else:
                    content = f.read()

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            return f"✅ File content ({len(content)} chars):\n{content}"

        except Exception as e:
            return f"❌ Error reading file '{file_path}': {str(e)}"

    def search_files(self, pattern: str, directory: str = ".", file_types: List[str] = None) -> str:
        """Search for files and content with advanced filtering"""
        try:
            if file_types is None:
                file_types = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json", "*.md", "*.txt", "*.yml", "*.yaml", "*.sh", "*.bash", "*.ps1", "*.cmd", "*.bat", "*.ini", "*.cfg", "*.conf", "*.xml", "*.csv", "*.log"]

            results = []
            search_dir = os.path.abspath(directory)

            for file_type in file_types:
                for file_path in glob.glob(os.path.join(search_dir, "**", file_type), recursive=True):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if re.search(pattern, content, re.IGNORECASE):
                                # Find matching lines
                                lines = content.split('\n')
                                matches = []
                                for i, line in enumerate(lines, 1):
                                    if re.search(pattern, line, re.IGNORECASE):
                                        matches.append(f"  Line {i}: {line.strip()}")

                                results.append(f"📁 {file_path}:\n" + "\n".join(matches[:5]))
                    except:
                        continue

            if results:
                return f"🔍 Found {len(results)} files matching '{pattern}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No files found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error searching files: {str(e)}"

    def get_web_info(self, query: str) -> str:
        """Retrieve information from web without search engine API"""
        try:
            # Simple web scraping for documentation and info
            encoded_query = urllib.parse.quote(query)
            urls = [
                f"https://docs.python.org/3/search.html?q={encoded_query}",
                f"https://developer.mozilla.org/en-US/search?q={encoded_query}",
                f"https://stackoverflow.com/search?q={encoded_query}"
            ]

            results = []
            for url in urls[:2]:  # Limit to avoid rate limiting
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        content = response.read().decode('utf-8')
                        # Extract useful text (simplified)
                        text_content = re.sub(r'<[^>]+>', '', content)
                        text_content = re.sub(r'\s+', ' ', text_content)
                        results.append(text_content[:500] + "...")
                except:
                    continue

            if results:
                return f"🌐 Web information for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ Could not retrieve web information for '{query}'"

        except Exception as e:
            return f"❌ Error retrieving web info: {str(e)}"

    def analyze_code(self, code: str, language: str = "python") -> str:
        """Advanced code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = {
                        "functions": [],
                        "classes": [],
                        "imports": [],
                        "variables": [],
                        "complexity": 0
                    }

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis["functions"].append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis["classes"].append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis["imports"].append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis["imports"].append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis["variables"].append(target.id)

                    return f"📊 Code Analysis:\n{json.dumps(analysis, indent=2)}"
                except SyntaxError as e:
                    return f"❌ Syntax Error in code: {str(e)}"
            else:
                return f"🔍 Basic analysis for {language} code:\nLines: {len(code.split())}\nCharacters: {len(code)}"

        except Exception as e:
            return f"❌ Error analyzing code: {str(e)}"

    def fix_errors(self, error_log: str, code_context: str = "") -> str:
        """Advanced error analysis and fixing suggestions"""
        try:
            suggestions = []
            fixes = []

            # Common error patterns and fixes
            error_patterns = {
                r"ModuleNotFoundError.*'(\w+)'": lambda m: f"pip install {m.group(1)}",
                r"SyntaxError.*line (\d+)": lambda m: f"Check syntax on line {m.group(1)}",
                r"IndentationError": lambda m: "Fix indentation - use consistent spaces/tabs",
                r"NameError.*'(\w+)'": lambda m: f"Variable '{m.group(1)}' not defined - check spelling",
                r"FileNotFoundError.*'([^']+)'": lambda m: f"File '{m.group(1)}' not found - check path",
                r"port.*already in use": lambda m: "Change port number or kill existing process",
                r"Permission denied": lambda m: "Run with administrator privileges or check file permissions"
            }

            for pattern, fix_func in error_patterns.items():
                matches = re.finditer(pattern, error_log, re.IGNORECASE)
                for match in matches:
                    fix = fix_func(match)
                    if fix not in fixes:
                        fixes.append(fix)

            # AI-powered suggestions based on context
            if code_context:
                if "import" in error_log.lower() and "module" in error_log.lower():
                    missing_modules = re.findall(r"No module named '(\w+)'", error_log)
                    for module in missing_modules:
                        fixes.append(f"Install missing module: pip install {module}")

            if fixes:
                return f"🔧 Error Analysis & Fixes:\n" + "\n".join([f"• {fix}" for fix in fixes])
            else:
                return f"🤔 Complex error detected. Manual review needed:\n{error_log[:500]}"

        except Exception as e:
            return f"❌ Error analyzing error log: {str(e)}"

    def generate_code(self, description: str, language: str = "python") -> str:
        """AI-powered code generation"""
        try:
            prompt = f"""Generate {language} code for: {description}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comments for complex logic
- Follow best practices for {language}
- Make it modular and reusable

Code:"""

            # Use AI provider directly
            generated_code = ai_provider.make_request(prompt)

            # Extract code from response
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', generated_code, re.DOTALL)
            if code_match:
                return f"🤖 Generated {language} code:\n```{language}\n{code_match.group(1)}\n```"
            else:
                return f"🤖 Generated {language} code:\n{generated_code}"

        except Exception as e:
            return f"❌ Error generating code: {str(e)}"

    def refactor_code(self, code: str, refactor_type: str = "optimize") -> str:
        """AI-powered code refactoring"""
        try:
            refactor_prompts = {
                "optimize": "Optimize this code for better performance and readability",
                "modularize": "Break this code into smaller, reusable functions/modules",
                "clean": "Clean up this code - remove duplicates, improve naming, add comments",
                "secure": "Make this code more secure - fix potential vulnerabilities"
            }

            prompt = f"""{refactor_prompts.get(refactor_type, refactor_prompts['optimize'])}:

Original Code:
```
{code}
```

Refactored Code:"""

            response = ai_provider.make_request(prompt)
            return f"🔄 Refactored code ({refactor_type}):\n{response}"

        except Exception as e:
            return f"❌ Error refactoring code: {str(e)}"

    def get_project_structure(self, directory: str = ".") -> str:
        """Get comprehensive project structure"""
        try:
            structure = {}

            def build_tree(path, max_depth=3, current_depth=0):
                if current_depth >= max_depth:
                    return "..."

                items = {}
                try:
                    for item in sorted(os.listdir(path)):
                        if item.startswith('.'):
                            continue
                        item_path = os.path.join(path, item)
                        if os.path.isdir(item_path):
                            items[f"📁 {item}/"] = build_tree(item_path, max_depth, current_depth + 1)
                        else:
                            size = os.path.getsize(item_path)
                            items[f"📄 {item}"] = f"{size} bytes"
                except PermissionError:
                    items["❌ Permission Denied"] = ""
                return items

            structure = build_tree(os.path.abspath(directory))
            self.context.project_structure = structure

            return f"📂 Project Structure:\n{json.dumps(structure, indent=2)}"

        except Exception as e:
            return f"❌ Error getting project structure: {str(e)}"

    def run_tests(self, test_path: str = ".", test_type: str = "auto") -> str:
        """Run tests with auto-detection"""
        try:
            test_commands = {
                "python": ["python -m pytest", "python -m unittest discover"],
                "javascript": ["npm test", "yarn test", "jest"],
                "node": ["npm test", "mocha"],
                "auto": []
            }

            if test_type == "auto":
                # Auto-detect test framework
                if os.path.exists("package.json"):
                    test_commands["auto"] = test_commands["javascript"]
                elif any(f.endswith(".py") for f in os.listdir(".")):
                    test_commands["auto"] = test_commands["python"]
                else:
                    return "❌ Could not auto-detect test framework"

            commands = test_commands.get(test_type, test_commands["auto"])

            for cmd in commands:
                result = self.run_command(cmd)
                if "✅" in result:
                    return f"🧪 Tests executed:\n{result}"

            return "❌ No suitable test command found"

        except Exception as e:
            return f"❌ Error running tests: {str(e)}"

    def show_help(self):
        """Show comprehensive help information"""
        help_text = """
🤖 ADVANCED CLI CODING AGENT v2.0 - COMPREHENSIVE HELP

🎯 ENTERPRISE-LEVEL CAPABILITIES:
• Build complete applications in 10+ programming languages
• Cross-language code conversion (Python ↔ JavaScript ↔ TypeScript ↔ C++ ↔ Java ↔ Go)
• Deep code analysis with security and performance auditing
• Multi-step automated pipelines (Generate → Run → Fix → Refactor → Optimize)
• Enhanced web research with Stack Overflow, GitHub, and documentation integration
• Advanced Git operations and automated version control
• Intelligent package management across all major ecosystems
• Predictive suggestions with background processing
• Real-time performance profiling and optimization

💡 EXAMPLE COMMANDS:

🏗️ PROJECT CREATION & MANAGEMENT:
• "Create a full-stack React TypeScript app with authentication"
• "Build a Python FastAPI microservice with Docker"
• "Set up a Rust CLI application with error handling"
• "Initialize a Node.js project with Express and MongoDB"

🔄 CODE TRANSFORMATION & ANALYSIS:
• "Convert this Python function to JavaScript"
• "Analyze the security vulnerabilities in my code"
• "Profile the performance of this algorithm"
• "Refactor this code for better maintainability"
• "Generate comprehensive unit tests for my module"

🔍 INTELLIGENT RESEARCH & DEBUGGING:
• "Search Stack Overflow for React hooks best practices"
• "Find GitHub examples of JWT authentication"
• "Debug this error and provide automated fixes"
• "Research the latest TypeScript features"

📦 DEPENDENCY & VERSION CONTROL:
• "Install and configure all project dependencies"
• "Commit my changes with an intelligent message"
• "Update all packages to latest versions"
• "Set up automated testing pipeline"

🧠 SMART AUTOMATION:
• "Run the complete development workflow"
• "Optimize my code for production deployment"
• "Set up CI/CD pipeline with GitHub Actions"
• "Generate API documentation automatically"

🔧 SPECIAL COMMANDS:
• help - Show this comprehensive help
• status - Show detailed agent status and context
• suggestions - Get smart suggestions based on current context
• pipeline [description] - Run multi-step automation pipeline
• convert [code] [from_lang] [to_lang] - Convert code between languages
• audit [code] - Perform security and performance audit
• profile [code] - Profile code performance
• git [operation] - Perform Git operations
• install [package] - Install packages with auto-detection
• /switch [provider] - Switch AI provider (mistral, deepseek, gemini, openai, anthropic)
• exit/quit - Exit the agent

🛠️ OPENCODE-STYLE TOOLS:
• view file.py [offset=10] [limit=50] - Enhanced file viewing with line numbers
• edit file.py content="code" [operation=replace] - Advanced file editing with diff
• write file.py content="code" - Simple file writing
• bash "command" [timeout=60] - Execute shell commands with security controls
• glob "*.py" [path=src] - Find files using glob patterns
• grep "pattern" [path=src] [include="*.py"] - Search file contents
• ls [path=.] [show_hidden=false] - Enhanced directory listing
• fetch url="https://..." format=json - Fetch web content

🚀 AUTONOMOUS ENTERPRISE FEATURES:
• Predictive prefetching of next likely actions
• Context-aware intelligent suggestions
• Auto-detection of project type and requirements
• Cross-language code translation and optimization
• Multi-threaded execution with zero-lag responses
• Background error monitoring and auto-fixing
• Intelligent Git workflow automation
• Performance optimization recommendations
• Security vulnerability detection and remediation
• Automated code review and quality enforcement
• Documentation generation and maintenance
• Real-time project health monitoring

🧠 INTELLIGENCE CAPABILITIES:
• Natural language understanding (English/Hindi)
• Pattern recognition for task automation
• Contextual learning from user preferences
• Predictive code completion and suggestions
• Chain-of-thought reasoning for complex problems
• Self-critique and continuous improvement
• Multi-source web research and synthesis
• Automated testing and validation

🔒 SECURITY & PERFORMANCE:
• Comprehensive security auditing
• Performance profiling and optimization
• Code quality enforcement
• Best practices validation
• Vulnerability detection and remediation
• Automated security patches

📊 ANALYTICS & MONITORING:
• Real-time performance metrics
• Code complexity analysis
• Project health monitoring
• Development productivity tracking
• Error pattern analysis
• Optimization recommendations
"""
        print(help_text)

    def show_status(self):
        """Show comprehensive agent status"""
        try:
            # Get Git status
            git_status = self.git_manager.get_git_status()

            # Get project type
            project_type = self.package_manager.detect_project_type()

            # Get predictive suggestions
            predictions = self.context.predictive_cache.next_actions[:3]

            # Get performance metrics
            import psutil
            memory_percent = psutil.virtual_memory().percent
            cpu_percent = psutil.cpu_percent()

            print(f"""
📊 ADVANCED AGENT STATUS DASHBOARD:

🏠 ENVIRONMENT:
• Current Directory: {self.context.current_directory}
• Project Type: {project_type.title()}
• Operating System: {os.name}
• Memory Usage: {memory_percent}%
• CPU Usage: {cpu_percent}%

📁 PROJECT CONTEXT:
• Active Files: {len(self.context.active_files)} files
• Command History: {len(self.context.command_history)} commands
• Working Memory: {len(self.context.working_memory)} items
• Cache Size: {len(self.cache)} cached items
• Conversation Memory: {len(self.memory.buffer)} messages

🔄 GIT STATUS:
• Repository: {'✅ Active' if git_status.get('is_git_repo') else '❌ Not a Git repo'}
• Current Branch: {git_status.get('current_branch', 'N/A')}
• Changes: {'✅ Clean' if not git_status.get('has_changes') else f"⚠️ {len(git_status.get('modified_files', []))} modified files"}

🧠 INTELLIGENCE STATUS:
• Predictive Cache: {'✅ Active' if predictions else '⏸️ Idle'}
• Background Processing: ✅ Running
• Pattern Analysis: ✅ Learning
• Last Error: {self.context.last_error or '✅ None'}

🔮 PREDICTIVE SUGGESTIONS:
{chr(10).join([f"  • {pred}" for pred in predictions]) if predictions else "  • No predictions available"}

📁 RECENT FILES:
{chr(10).join([f"  • {Path(f).name} ({Path(f).suffix})" for f in self.context.active_files[-5:]]) if self.context.active_files else "  • No recent files"}

⚡ RECENT COMMANDS:
{chr(10).join([f"  • {cmd[:50]}{'...' if len(cmd) > 50 else ''}" for cmd in self.context.command_history[-3:]]) if self.context.command_history else "  • No recent commands"}

🎯 CAPABILITIES STATUS:
• Code Analysis: ✅ Ready
• Cross-Language Conversion: ✅ Ready
• Security Auditing: ✅ Ready
• Performance Profiling: ✅ Ready
• Web Research: ✅ Ready
• Package Management: ✅ Ready
• Git Operations: ✅ Ready
• Multi-Step Pipelines: ✅ Ready

💡 QUICK ACTIONS:
• Type 'suggestions' for context-aware recommendations
• Type 'help' for comprehensive capabilities guide
• Type 'pipeline [description]' for automated workflows
""")
        except Exception as e:
            print(f"❌ Error displaying status: {str(e)}")
            print("📊 Basic Status: Agent is running but status details unavailable")

    def run_agent(self):
        """Main agent execution loop with enhanced capabilities"""
        print("🤖 Advanced CLI Coding Agent v2.0 - Enterprise Edition")
        print("=" * 70)
        print("🎯 I can help you build anything with 25+ advanced tools!")
        print("💡 Examples: 'Create a React app', 'Convert Python to JS', 'Audit my code'")
        print("🔧 Type 'help' for full capabilities, 'status' for dashboard, 'exit' to quit")
        print("=" * 70)

        # Check if LangChain is available for full agent mode
        if not LANGCHAIN_AVAILABLE:
            print("⚠️ LangChain not available. Running in basic mode.")
            print("💡 Install LangChain for full agent capabilities: pip install langchain langchain-community langchain-google-genai")
            self._run_basic_mode()
            return

        # Create agent with enhanced tools
        tools = self.create_tools()

        # Initialize LLM
        try:
            # Try to use Google Gemini via LangChain
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash",
                google_api_key=os.getenv("GEMINI_API_KEY"),
                temperature=0.1
            )
        except Exception as e:
            print(f"⚠️ Failed to initialize LLM: {e}")
            print("💡 Falling back to basic mode...")
            self._run_basic_mode()
            return

        # Create enhanced prompt template
        prompt_template = self.create_agent_prompt() + """

TOOLS:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times if need else give final answer)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""

        prompt = PromptTemplate(
            input_variables=["input", "agent_scratchpad", "tools", "tool_names"],
            template=prompt_template
        )

        # Create enhanced agent
        agent = create_react_agent(llm, tools, prompt)
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=15,
            early_stopping_method="generate"
        )

        while True:
            try:
                user_input = input("\n🤖 agent> ").strip()

                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print("👋 Goodbye! Happy coding with your enhanced AI agent!")
                    break

                if user_input.lower() == 'help':
                    self.show_help()
                    continue

                if user_input.lower() == 'status':
                    self.show_status()
                    continue

                if user_input.lower() == 'suggestions':
                    suggestions = self.smart_code_suggestions("")
                    print(suggestions)
                    continue

                # Handle /switch command for provider switching
                if user_input.lower().startswith('/switch'):
                    parts = user_input.split()
                    if len(parts) == 1:
                        # Show current provider status
                        print(ai_provider.get_provider_status())
                        continue
                    elif len(parts) == 2:
                        provider_name = parts[1]
                        result = ai_provider.switch_provider(provider_name)
                        print(result)
                        continue
                    else:
                        print("❌ Usage: /switch [provider_name]")
                        print("Available providers: mistral, deepseek, gemini, openai, anthropic")
                        continue

                # Handle OpenCode-style tool commands
                if user_input.startswith(('view ', 'edit ', 'write ', 'bash ', 'glob ', 'grep ', 'ls ', 'fetch ')):
                    result = self._handle_opencode_command(user_input)
                    print(f"\n📋 Result: {result}")
                    continue

                if not user_input:
                    continue

                # Add enhanced context to user input
                context_info = f"""
🏠 Current Context:
- Directory: {self.context.current_directory}
- Project Type: {self.package_manager.detect_project_type()}
- Active Files: {", ".join([Path(f).name for f in self.context.active_files[-3:]]) if self.context.active_files else "None"}
- Git Status: {self.git_manager.get_git_status().get('current_branch', 'No Git')}
- Recent Commands: {", ".join(self.context.command_history[-2:]) if self.context.command_history else "None"}

🎯 User Request: {user_input}

💡 Available Advanced Capabilities:
- Cross-language code conversion
- Security and performance auditing
- Multi-step automated pipelines
- Enhanced web research
- Git operations and automation
- Package management
- Predictive suggestions
- Real-time code analysis"""

                # Execute with enhanced context
                print(f"\n🧠 Processing: {user_input}")
                print("-" * 60)

                # Run agent with comprehensive error handling
                try:
                    result = agent_executor.invoke({
                        "input": context_info
                    })

                    if result.get("output"):
                        print(f"\n✅ Task completed successfully!")
                        print(f"📋 Result: {result['output']}")

                        # Generate smart suggestions for next steps
                        suggestions = self.smart_code_suggestions(user_input)
                        if "💡" in suggestions:
                            print(f"\n{suggestions}")

                except Exception as e:
                    print(f"❌ Agent execution error: {str(e)}")
                    # Try to provide intelligent error recovery
                    fix_suggestion = self.fix_errors(str(e))
                    print(f"🔧 Recovery suggestion: {fix_suggestion}")

                    # Offer alternative approaches
                    print("\n💡 Alternative approaches:")
                    print("  • Try breaking down the request into smaller steps")
                    print("  • Use specific tool commands (e.g., 'generate_code', 'analyze_code')")
                    print("  • Check 'status' for current context")

            except KeyboardInterrupt:
                print("\n⏸️ Interrupted. Type 'exit' to quit or continue with new command.")
                continue
            except Exception as e:
                print(f"❌ Unexpected error: {str(e)}")
                print("🔄 Agent recovering... Please try again.")
                continue

    def _run_basic_mode(self):
        """Run agent in basic mode without LangChain"""
        print("\n🔧 Running in Basic Mode - Direct tool access")
        print("💡 Available commands: run_command, write_file, read_file, search_files, analyze_code, generate_code")
        print("🛠️ OpenCode tools: view, edit, write, bash, glob, grep, ls, fetch")
        print("🔄 Special: /switch [provider], help, status, exit")

        while True:
            try:
                user_input = input("\n🤖 basic> ").strip()

                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print("👋 Goodbye! Happy coding!")
                    break

                if user_input.lower() == 'help':
                    self.show_help()
                    continue

                if user_input.lower() == 'status':
                    self.show_status()
                    continue

                # Handle /switch command
                if user_input.lower().startswith('/switch'):
                    parts = user_input.split()
                    if len(parts) == 1:
                        print(ai_provider.get_provider_status())
                        continue
                    elif len(parts) == 2:
                        provider_name = parts[1]
                        result = ai_provider.switch_provider(provider_name)
                        print(result)
                        continue
                    else:
                        print("❌ Usage: /switch [provider_name]")
                        print("Available providers: mistral, deepseek, gemini, openai, anthropic")
                        continue

                # Handle OpenCode-style tool commands
                if user_input.startswith(('view ', 'edit ', 'write ', 'bash ', 'glob ', 'grep ', 'ls ', 'fetch ')):
                    result = self._handle_opencode_command(user_input)
                    print(f"\n📋 Result: {result}")
                    continue

                if not user_input:
                    continue

                # Parse basic commands
                parts = user_input.split(' ', 1)
                command = parts[0].lower()
                args = parts[1] if len(parts) > 1 else ""

                # Execute basic commands
                if command == 'run_command':
                    result = self.run_command(args)
                elif command == 'write_file':
                    if '|' in args:
                        path, content = args.split('|', 1)
                        result = self.write_file(path.strip(), content.strip())
                    else:
                        result = "❌ Format: write_file path|content"
                elif command == 'read_file':
                    result = self.read_file(args)
                elif command == 'search_files':
                    result = self.search_files(args)
                elif command == 'analyze_code':
                    result = self.analyze_code(args)
                elif command == 'generate_code':
                    result = self.generate_code(args)
                else:
                    result = f"❌ Unknown command: {command}\n💡 Try: run_command, write_file, read_file, search_files, analyze_code, generate_code"
                    result += f"\n🛠️ Or use OpenCode tools: view, edit, bash, glob, grep, ls, fetch"

                print(f"\n📋 Result: {result}")

            except KeyboardInterrupt:
                print("\n⏸️ Interrupted. Type 'exit' to quit.")
                continue
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                continue

    def create_tools(self):
        """Create LangChain tools from agent methods"""
        return [
            # Core Tools
            Tool(
                name="run_command",
                description="Execute PowerShell/terminal commands with advanced error handling",
                func=lambda cmd: self.run_command(cmd)
            ),
            Tool(
                name="write_file",
                description="Write files with backup and validation. Format: path|content",
                func=lambda args: self.write_file(args.split("|")[0], "|".join(args.split("|")[1:]))
            ),
            Tool(
                name="read_file",
                description="Read files with line range support",
                func=lambda path: self.read_file(path)
            ),
            Tool(
                name="search_files",
                description="Search for files and content with pattern matching",
                func=lambda pattern: self.search_files(pattern)
            ),

            # Enhanced Web and Information Tools
            Tool(
                name="enhanced_web_search",
                description="Enhanced web search with Stack Overflow, GitHub, and documentation",
                func=lambda query: self.enhanced_web_search(query)
            ),
            Tool(
                name="get_web_info",
                description="Basic web information retrieval",
                func=lambda query: self.get_web_info(query)
            ),

            # Advanced Code Analysis Tools
            Tool(
                name="analyze_code",
                description="Deep code analysis with security and performance checks",
                func=lambda code: self.analyze_code(code)
            ),
            Tool(
                name="security_audit",
                description="Perform security audit on code. Format: code|language",
                func=lambda args: self.security_audit(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),
            Tool(
                name="performance_profile",
                description="Profile code performance. Format: code|language",
                func=lambda args: self.performance_profile(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),

            # Code Generation and Refactoring Tools
            Tool(
                name="generate_code",
                description="AI-powered code generation",
                func=lambda desc: self.generate_code(desc)
            ),
            Tool(
                name="refactor_code",
                description="AI-powered code refactoring",
                func=lambda code: self.refactor_code(code)
            ),
            Tool(
                name="cross_language_convert",
                description="Convert code between languages. Format: code|from_lang|to_lang",
                func=lambda args: self.cross_language_convert(*args.split("|"))
            ),

            # Multi-Step Pipeline Tools
            Tool(
                name="multi_step_pipeline",
                description="Execute complete code-run-fix-refactor pipeline. Format: description|language",
                func=lambda args: self.multi_step_code_pipeline(args.split("|")[0], args.split("|")[1] if "|" in args else "python")
            ),
            Tool(
                name="smart_suggestions",
                description="Generate smart code suggestions based on context",
                func=lambda context: self.smart_code_suggestions(context)
            ),

            # Error Handling and Debugging Tools
            Tool(
                name="fix_errors",
                description="Advanced error analysis and fixing suggestions",
                func=lambda error: self.fix_errors(error)
            ),

            # Git Integration Tools
            Tool(
                name="git_status",
                description="Get Git repository status",
                func=lambda _: json.dumps(self.git_manager.get_git_status(), indent=2)
            ),
            Tool(
                name="git_operation",
                description="Perform Git operations. Format: operation|args (e.g., 'commit|Initial commit')",
                func=lambda args: self.git_operations(args.split("|")[0], args.split("|")[1] if "|" in args else "")
            ),
            Tool(
                name="auto_commit_push",
                description="Automatically commit and push changes",
                func=lambda message: self.git_manager.auto_commit_and_push(message if message else "Auto-commit by AI Agent")
            ),

            # Package Management Tools
            Tool(
                name="install_package",
                description="Install a package using appropriate package manager",
                func=lambda package: self.package_manager.install_package(package)
            ),
            Tool(
                name="package_operation",
                description="Manage packages. Format: action|package (e.g., 'install|requests')",
                func=lambda args: self.package_operations(args.split("|")[0], args.split("|")[1] if "|" in args else "")
            ),
            Tool(
                name="detect_project_type",
                description="Auto-detect project type based on files",
                func=lambda _: self.package_manager.detect_project_type()
            ),

            # System and Project Tools
            Tool(
                name="get_project_structure",
                description="Get comprehensive project structure",
                func=lambda dir: self.get_project_structure(dir if dir else ".")
            ),
            Tool(
                name="get_system_info",
                description="Get comprehensive system information including Git and project status",
                func=lambda _: self.get_system_info()
            ),
            Tool(
                name="run_tests",
                description="Run tests with auto-detection",
                func=lambda path: self.run_tests(path if path else ".")
            )
        ]

    def create_agent_prompt(self):
        """Create comprehensive system prompt for the agent"""
        return """You are an ADVANCED AUTONOMOUS CLI CODING AGENT powered by Gemini AI with ENTERPRISE-LEVEL capabilities.

🎯 CORE CAPABILITIES:
- Full-stack development (Python, JavaScript, TypeScript, React, Node.js, Rust, Go, Java, C++, etc.)
- Advanced file operations and project management
- Terminal command execution with PowerShell support
- Deep code analysis with security and performance auditing
- Cross-language code conversion and translation
- Error detection and autonomous fixing
- Enhanced web information retrieval with multiple sources
- Multi-threaded task execution with predictive prefetching
- Context-aware decision making with smart suggestions

🧠 ADVANCED INTELLIGENCE FEATURES:
- Natural language processing for user commands (English/Hindi)
- Predictive prefetching of likely next actions with background processing
- Chain-of-thought reasoning for complex problems
- Self-critique and optimization with continuous improvement
- Context compression and smart suggestions based on patterns
- Autonomous debugging and error resolution with auto-fixing
- Cross-language integration and code translation
- Performance profiling and security auditing
- Multi-step code pipeline (Generate → Run → Fix → Refactor → Optimize)

🔄 ENHANCED WORKFLOW PROCESS:
1. ANALYZE: Deep understanding of user input and comprehensive context analysis
2. PREDICT: Background prediction of next likely actions and suggestions
3. PLAN: Create detailed step-by-step execution plan with alternatives
4. EXECUTE: Perform one action at a time with real-time monitoring
5. OBSERVE: Analyze results, performance, and security implications
6. ADAPT: Adjust plan based on observations and learned patterns
7. OPTIMIZE: Continuous improvement and refactoring suggestions
8. CONTINUE: Iterate until optimal solution is achieved

RESPOND WITH NATURAL LANGUAGE - NO JSON FORMAT REQUIRED.
Be conversational, helpful, and demonstrate your advanced enterprise capabilities.
Always explain what you're doing, why you're doing it, and what the expected outcome is.
Provide intelligent suggestions and anticipate user needs based on context.
-RULES
1. Always maintain context awareness - track user's recent actions, files, and preferences
2. Use predictive intelligence - anticipate next likely actions based on patterns
3. Provide multi-step solutions - break down complex tasks into manageable steps
4. Maintain code quality standards - enforce best practices and clean code principles
5. Enable cross-language compatibility - seamlessly handle multiple programming languages
6. Ensure security first - scan for vulnerabilities and suggest secure alternatives
7. Focus on performance - optimize code and suggest performance improvements
8. Support enterprise features - handle large codebases and team collaboration
9. Enable continuous learning - adapt suggestions based on user feedback and patterns
10. Maintain conversation history - use past interactions to improve future suggestions
11. Handle errors gracefully - provide clear error messages and recovery steps
12. Support multiple paradigms - work with different programming styles and frameworks
13. Enable customization - allow user preferences and custom rules
14. Maintain documentation - generate and update documentation automatically
15. Support testing - suggest and generate tests for code changes
16. Enable version control - integrate with git and other VCS systems
17. Support package management - handle dependencies across ecosystems
18. Enable code analysis - provide insights about code quality and complexity
19. Support refactoring - suggest and implement code improvements
20. Maintain consistency - enforce coding standards and style guides
21. If there is need then use git or github tools else ''DONT USE GITHUB TOOLS''
"""

    def smart_code_suggestions(self, context: str) -> str:
        """Generate smart code suggestions based on context"""
        try:
            suggestions = []

            # Get predictive suggestions
            predictions = self.context.predictive_cache.next_actions
            if predictions:
                suggestions.extend([f"🔮 Predicted: {pred}" for pred in predictions[:3]])

            # Analyze current context
            if self.context.active_files:
                latest_file = self.context.active_files[-1]
                if latest_file.endswith('.py'):
                    suggestions.extend([
                        "🐍 Add type hints to functions",
                        "🧪 Generate unit tests",
                        "📝 Add docstrings",
                        "🔧 Run linting (flake8/black)"
                    ])
                elif latest_file.endswith(('.js', '.ts')):
                    suggestions.extend([
                        "⚡ Add TypeScript types",
                        "🧪 Add Jest tests",
                        "📦 Check npm dependencies",
                        "🔧 Run ESLint"
                    ])

            # Git-based suggestions
            git_status = self.git_manager.get_git_status()
            if git_status.get('has_changes'):
                suggestions.append("📝 Commit and push changes")

            if suggestions:
                return "💡 Smart Suggestions:\n" + "\n".join([f"  • {s}" for s in suggestions])
            else:
                return "✅ No specific suggestions at the moment"

        except Exception as e:
            return f"❌ Error generating suggestions: {str(e)}"

    def _handle_opencode_command(self, user_input: str) -> str:
        """Handle OpenCode-style tool commands"""
        try:
            parts = user_input.split(' ', 1)
            tool_name = parts[0].lower()

            if tool_name not in self.opencode_tools:
                return f"❌ Unknown tool: {tool_name}"

            # Parse arguments (simplified JSON-like parsing)
            if len(parts) > 1:
                args_str = parts[1]

                # Simple argument parsing for common patterns
                if tool_name == 'view':
                    # Handle: view file.py or view file.py offset=10 limit=50
                    args_parts = args_str.split()
                    file_path = args_parts[0]
                    params = {"file_path": file_path}

                    for arg in args_parts[1:]:
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            try:
                                params[key] = int(value)
                            except ValueError:
                                params[key] = value

                elif tool_name in ['edit', 'write']:
                    # Handle: edit file.py content="code here"
                    if 'content=' in args_str:
                        file_part, content_part = args_str.split('content=', 1)
                        file_path = file_part.strip()
                        content = content_part.strip('"\'')
                        params = {"file_path": file_path, "content": content}
                    else:
                        params = {"file_path": args_str}

                elif tool_name == 'bash':
                    # Handle: bash "command here"
                    command = args_str.strip('"\'')
                    params = {"command": command}

                elif tool_name in ['glob', 'grep']:
                    # Handle: glob "*.py" or grep "pattern" path=src
                    args_parts = args_str.split()
                    pattern = args_parts[0].strip('"\'')
                    params = {"pattern": pattern}

                    for arg in args_parts[1:]:
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            params[key] = value.strip('"\'')

                elif tool_name == 'ls':
                    # Handle: ls path=src show_hidden=true
                    params = {}
                    args_parts = args_str.split()

                    if args_parts and not '=' in args_parts[0]:
                        params["path"] = args_parts[0]
                        args_parts = args_parts[1:]

                    for arg in args_parts:
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            if value.lower() in ['true', 'false']:
                                params[key] = value.lower() == 'true'
                            else:
                                params[key] = value.strip('"\'')

                elif tool_name == 'fetch':
                    # Handle: fetch url="https://..." format=json
                    args_parts = args_str.split()
                    params = {}

                    for arg in args_parts:
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            params[key] = value.strip('"\'')
                        elif arg.startswith('http'):
                            params["url"] = arg

                else:
                    params = {"input": args_str}
            else:
                params = {}

            # Create tool call
            tool_call = ToolCall(
                id=str(uuid.uuid4()),
                name=tool_name,
                input=json.dumps(params)
            )

            # Execute tool
            tool = self.opencode_tools[tool_name]
            response = tool.run({}, tool_call)

            if response.is_error:
                return f"❌ {response.content}"
            else:
                return response.content

        except Exception as e:
            return f"❌ Error executing OpenCode command: {str(e)}"

    def multi_step_code_pipeline(self, description: str, language: str = "python") -> str:
        """Execute complete code-run-fix-refactor pipeline"""
        try:
            pipeline_results = []

            # Step 1: Generate Code
            pipeline_results.append("🔄 Step 1: Generating code...")
            code_result = self.generate_code(description, language)
            pipeline_results.append(code_result)

            # Extract generated code
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', code_result, re.DOTALL)
            if not code_match:
                return "❌ Failed to extract generated code"

            generated_code = code_match.group(1)

            # Step 2: Analyze Code
            pipeline_results.append("\n🔄 Step 2: Analyzing code...")
            analysis = self.analyze_code(generated_code, language)
            pipeline_results.append(f"📊 Analysis: {analysis}")

            # Step 3: Write and Test Code
            pipeline_results.append("\n🔄 Step 3: Writing and testing code...")
            filename = f"generated_{int(time.time())}.{language}"
            write_result = self.write_file(filename, generated_code)
            pipeline_results.append(write_result)

            # Step 4: Run Code (if Python)
            if language == "python":
                pipeline_results.append("\n🔄 Step 4: Running code...")
                run_result = self.run_command(f"python {filename}")
                pipeline_results.append(run_result)

                # Step 5: Fix errors if any
                if "❌" in run_result:
                    pipeline_results.append("\n🔄 Step 5: Fixing errors...")
                    fix_result = self.fix_errors(run_result, generated_code)
                    pipeline_results.append(fix_result)

            # Step 6: Refactor Code
            pipeline_results.append("\n🔄 Step 6: Refactoring code...")
            refactor_result = self.refactor_code(generated_code)
            pipeline_results.append(refactor_result)

            return "\n".join(pipeline_results)

        except Exception as e:
            return f"❌ Pipeline error: {str(e)}"

    def cross_language_convert(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        return self.language_converter.convert_code(code, from_lang, to_lang)

    def enhanced_web_search(self, query: str) -> str:
        """Enhanced web search with multiple sources"""
        context = " ".join(self.context.command_history[-3:]) if self.context.command_history else ""
        return self.web_scraper.enhanced_web_search(query, context)

    def git_operations(self, operation: str, args: str = "") -> str:
        """Perform Git operations"""
        return self.git_manager.git_operation(operation, args)

    def package_operations(self, action: str, package: str = "") -> str:
        """Manage packages and dependencies"""
        return self.package_manager.manage_dependencies(action, package)

    def performance_profile(self, code: str, language: str = "python") -> str:
        """Profile code performance"""
        try:
            if language.lower() != "python":
                return "❌ Performance profiling currently only supports Python"

            # Create temporary file
            temp_file = f"profile_temp_{int(time.time())}.py"

            # Add profiling code
            profiled_code = f"""
import cProfile
import pstats
import io

def profile_target():
{chr(10).join(['    ' + line for line in code.split(chr(10))])}

if __name__ == "__main__":
    pr = cProfile.Profile()
    pr.enable()
    profile_target()
    pr.disable()

    s = io.StringIO()
    ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
    ps.print_stats(10)
    print(s.getvalue())
"""

            # Write and run profiled code
            self.write_file(temp_file, profiled_code)
            result = self.run_command(f"python {temp_file}")

            # Clean up
            if os.path.exists(temp_file):
                os.remove(temp_file)

            return f"⚡ Performance Profile:\n{result}"

        except Exception as e:
            return f"❌ Performance profiling error: {str(e)}"

    def security_audit(self, code: str, language: str = "python") -> str:
        """Perform security audit on code"""
        try:
            analysis = self.code_analyzer.deep_analyze_code(code, language)

            if hasattr(analysis, 'security_issues') and analysis.security_issues:
                issues = "\n".join([f"  ⚠️ {issue}" for issue in analysis.security_issues])
                return f"🔒 Security Audit Results:\n{issues}\n\n💡 Recommendations:\n  • Use parameterized queries\n  • Validate all inputs\n  • Avoid hardcoded secrets"
            else:
                return "✅ No obvious security issues detected"

        except Exception as e:
            return f"❌ Security audit error: {str(e)}"

    def get_system_info(self) -> str:
        """Get comprehensive system information"""
        try:
            import platform
            import psutil

            # Get git status
            git_status = self.git_manager.get_git_status()

            # Get project type
            project_type = self.package_manager.detect_project_type()

            info = {
                "os": platform.system(),
                "version": platform.version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "current_dir": os.getcwd(),
                "user": os.getenv("USERNAME") or os.getenv("USER", "unknown"),
                "memory_usage": f"{psutil.virtual_memory().percent}%",
                "cpu_usage": f"{psutil.cpu_percent()}%",
                "git_status": git_status,
                "project_type": project_type,
                "active_files": len(self.context.active_files),
                "command_history": len(self.context.command_history)
            }
            return json.dumps(info, indent=2)
        except Exception as e:
            return f"Error getting system info: {str(e)}"

class AdvancedCodeAnalyzer:
    def __init__(self):
        self.language_parsers = {}
        self.security_patterns = {
            'sql_injection': [r'SELECT.*FROM.*WHERE.*=.*\+', r'INSERT.*VALUES.*\+'],
            'xss': [r'innerHTML.*\+', r'document\.write.*\+'],
            'path_traversal': [r'\.\./', r'\.\.\\'],
            'hardcoded_secrets': [r'password\s*=\s*["\'][^"\']+["\']', r'api_key\s*=\s*["\'][^"\']+["\']']
        }

    def deep_analyze_code(self, code: str, language: str = "python") -> CodeAnalysisResult:
        """Perform deep code analysis with security and performance checks"""
        result = CodeAnalysisResult()

        try:
            if language.lower() == "python":
                result = self._analyze_python_code(code)
            elif language.lower() in ["javascript", "typescript"]:
                result = self._analyze_js_code(code)
            else:
                result = self._analyze_generic_code(code, language)

            # Add security analysis
            result.security_issues = self._detect_security_issues(code)

            # Add performance analysis
            result.performance_issues = self._detect_performance_issues(code, language)

            # Generate refactoring suggestions
            result.refactor_suggestions = self._generate_refactor_suggestions(code, language)

        except Exception as e:
            logging.error(f"Code analysis error: {e}")

        return result

    def _analyze_python_code(self, code: str) -> CodeAnalysisResult:
        """Analyze Python code using AST"""
        result = CodeAnalysisResult()

        try:
            tree = ast.parse(code)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    result.functions.append(node.name)
                    # Calculate complexity
                    result.complexity += self._calculate_complexity(node)
                elif isinstance(node, ast.ClassDef):
                    result.classes.append(node.name)
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        result.imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        result.imports.append(f"from {node.module}")

            # Detect code duplicates
            result.duplicates = self._detect_duplicates(code)

        except SyntaxError as e:
            result.security_issues.append(f"Syntax Error: {str(e)}")

        return result

    def _analyze_js_code(self, code: str) -> CodeAnalysisResult:
        """Analyze JavaScript/TypeScript code"""
        result = CodeAnalysisResult()

        # Basic regex-based analysis for JS
        function_pattern = r'function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=>\s*'
        class_pattern = r'class\s+(\w+)'
        import_pattern = r'import.*from\s+["\']([^"\']+)["\']|require\(["\']([^"\']+)["\']\)'

        functions = re.findall(function_pattern, code)
        classes = re.findall(class_pattern, code)
        imports = re.findall(import_pattern, code)

        result.functions = [f[0] or f[1] or f[2] for f in functions if any(f)]
        result.classes = classes
        result.imports = [i[0] or i[1] for i in imports if any(i)]
        result.complexity = len(result.functions) * 2 + len(result.classes) * 3

        return result

    def _analyze_generic_code(self, code: str, language: str) -> CodeAnalysisResult:
        """Generic code analysis for other languages"""
        result = CodeAnalysisResult()

        lines = code.split('\n')
        result.complexity = len([line for line in lines if line.strip() and not line.strip().startswith('#')])

        return result

    def _calculate_complexity(self, node) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
        return complexity

    def _detect_duplicates(self, code: str) -> List[Dict]:
        """Detect code duplicates"""
        lines = code.split('\n')
        duplicates = []

        for i, line1 in enumerate(lines):
            if len(line1.strip()) < 10:  # Skip short lines
                continue
            for j, line2 in enumerate(lines[i+1:], i+1):
                if line1.strip() == line2.strip():
                    duplicates.append({
                        'line1': i+1,
                        'line2': j+1,
                        'content': line1.strip()
                    })

        return duplicates

    def _detect_security_issues(self, code: str) -> List[str]:
        """Detect security vulnerabilities"""
        issues = []

        for issue_type, patterns in self.security_patterns.items():
            for pattern in patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    issues.append(f"Potential {issue_type.replace('_', ' ')} vulnerability detected")

        return issues

    def _detect_performance_issues(self, code: str, language: str) -> List[str]:
        """Detect performance issues"""
        issues = []

        if language.lower() == "python":
            # Check for common Python performance issues
            if re.search(r'for.*in.*range\(len\(', code):
                issues.append("Use enumerate() instead of range(len()) for better performance")
            if re.search(r'\+.*str\(', code):
                issues.append("Consider using f-strings for string formatting")
            if re.search(r'\.append\(.*\)\s*\n.*\.append\(', code):
                issues.append("Consider using list comprehension for multiple appends")

        elif language.lower() in ["javascript", "typescript"]:
            if re.search(r'document\.getElementById', code):
                issues.append("Consider caching DOM elements for better performance")
            if re.search(r'for\s*\(.*\.length', code):
                issues.append("Cache array length in for loops")

        return issues

    def _generate_refactor_suggestions(self, code: str, language: str) -> List[str]:
        """Generate refactoring suggestions"""
        suggestions = []

        lines = code.split('\n')
        if len(lines) > 50:
            suggestions.append("Consider breaking this into smaller functions")

        if language.lower() == "python":
            if re.search(r'def\s+\w+.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n.*\n', code):
                suggestions.append("Function is too long, consider extracting smaller functions")
            if code.count('if') > 5:
                suggestions.append("Consider using polymorphism or strategy pattern for complex conditionals")

        return suggestions

class LanguageConverter:
    def __init__(self):
        self.conversion_templates = {
            'python_to_javascript': {
                'print': 'console.log',
                'def ': 'function ',
                'True': 'true',
                'False': 'false',
                'None': 'null',
                'elif': 'else if',
                'and': '&&',
                'or': '||',
                'not': '!',
                'len(': '.length',
                'range(': 'Array.from({length: ',
                'str(': 'String(',
                'int(': 'parseInt(',
                'float(': 'parseFloat('
            },
            'javascript_to_python': {
                'console.log': 'print',
                'function ': 'def ',
                'true': 'True',
                'false': 'False',
                'null': 'None',
                'else if': 'elif',
                '&&': 'and',
                '||': 'or',
                '!': 'not ',
                '.length': 'len(',
                'parseInt(': 'int(',
                'parseFloat(': 'float(',
                'String(': 'str('
            }
        }

    def convert_code(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        try:
            conversion_key = f"{from_lang.lower()}_to_{to_lang.lower()}"

            if conversion_key in self.conversion_templates:
                converted_code = code
                templates = self.conversion_templates[conversion_key]

                for old_syntax, new_syntax in templates.items():
                    converted_code = converted_code.replace(old_syntax, new_syntax)

                # Language-specific formatting
                if to_lang.lower() == 'python':
                    converted_code = self._format_for_python(converted_code)
                elif to_lang.lower() == 'javascript':
                    converted_code = self._format_for_javascript(converted_code)

                return f"🔄 Converted from {from_lang} to {to_lang}:\n```{to_lang.lower()}\n{converted_code}\n```"
            else:
                return f"❌ Conversion from {from_lang} to {to_lang} not yet supported"

        except Exception as e:
            return f"❌ Error converting code: {str(e)}"

    def _format_for_python(self, code: str) -> str:
        """Format code for Python syntax"""
        lines = code.split('\n')
        formatted_lines = []

        for line in lines:
            # Remove semicolons
            line = line.rstrip(';')
            # Fix indentation (basic)
            if line.strip().endswith(':'):
                formatted_lines.append(line)
            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def _format_for_javascript(self, code: str) -> str:
        """Format code for JavaScript syntax"""
        lines = code.split('\n')
        formatted_lines = []

        for line in lines:
            # Add semicolons
            if line.strip() and not line.strip().endswith((';', '{', '}')):
                line = line + ';'
            formatted_lines.append(line)

        return '\n'.join(formatted_lines)

class RefactoringEngine:
    def __init__(self):
        self.refactoring_patterns = {
            'extract_function': self._extract_function,
            'remove_duplicates': self._remove_duplicates,
            'optimize_imports': self._optimize_imports,
            'improve_naming': self._improve_naming,
            'add_type_hints': self._add_type_hints
        }

    def auto_refactor(self, code: str, language: str = "python") -> str:
        """Automatically refactor code for better quality"""
        try:
            refactored_code = code
            suggestions = []

            # Apply all refactoring patterns
            for pattern_name, pattern_func in self.refactoring_patterns.items():
                try:
                    result = pattern_func(refactored_code, language)
                    if result != refactored_code:
                        refactored_code = result
                        suggestions.append(f"Applied {pattern_name.replace('_', ' ')}")
                except Exception as e:
                    logging.error(f"Refactoring pattern {pattern_name} failed: {e}")

            if suggestions:
                return f"🔧 Refactored code:\n```{language}\n{refactored_code}\n```\n\n✅ Applied: {', '.join(suggestions)}"
            else:
                return f"✅ Code is already well-structured, no refactoring needed."

        except Exception as e:
            return f"❌ Error during refactoring: {str(e)}"

    def _extract_function(self, code: str, language: str) -> str:
        """Extract long functions into smaller ones"""
        if language.lower() != "python":
            return code

        lines = code.split('\n')
        refactored_lines = []
        current_function = []
        in_function = False
        function_indent = 0

        for line in lines:
            if line.strip().startswith('def ') and ':' in line:
                if current_function and len(current_function) > 20:
                    # Extract helper function
                    helper_func = self._create_helper_function(current_function)
                    refactored_lines.extend(helper_func)

                current_function = [line]
                in_function = True
                function_indent = len(line) - len(line.lstrip())
            elif in_function:
                if line.strip() and len(line) - len(line.lstrip()) <= function_indent and not line.startswith(' '):
                    in_function = False
                    refactored_lines.extend(current_function)
                    refactored_lines.append(line)
                    current_function = []
                else:
                    current_function.append(line)
            else:
                refactored_lines.append(line)

        if current_function:
            refactored_lines.extend(current_function)

        return '\n'.join(refactored_lines)

    def _create_helper_function(self, function_lines: List[str]) -> List[str]:
        """Create a helper function from code block"""
        # Simple helper function extraction
        helper_lines = []
        helper_lines.append("def helper_function():")
        helper_lines.append("    # Extracted helper function")
        for line in function_lines[10:15]:  # Extract middle part
            helper_lines.append("    " + line.strip())
        helper_lines.append("")
        return helper_lines

    def _remove_duplicates(self, code: str, language: str) -> str:
        """Remove duplicate code blocks"""
        lines = code.split('\n')
        seen_lines = set()
        unique_lines = []

        for line in lines:
            if line.strip() and line.strip() not in seen_lines:
                unique_lines.append(line)
                seen_lines.add(line.strip())
            elif not line.strip():  # Keep empty lines
                unique_lines.append(line)

        return '\n'.join(unique_lines)

    def _optimize_imports(self, code: str, language: str) -> str:
        """Optimize import statements"""
        if language.lower() != "python":
            return code

        lines = code.split('\n')
        imports = []
        other_lines = []

        for line in lines:
            if line.strip().startswith(('import ', 'from ')):
                imports.append(line)
            else:
                other_lines.append(line)

        # Sort and deduplicate imports
        unique_imports = list(set(imports))
        unique_imports.sort()

        # Combine imports and other code
        result = unique_imports + [''] + other_lines
        return '\n'.join(result)

    def _improve_naming(self, code: str, language: str) -> str:
        """Improve variable and function naming"""
        # Basic naming improvements
        improvements = {
            'temp': 'temporary_value',
            'tmp': 'temporary',
            'i': 'index',
            'j': 'inner_index',
            'x': 'value',
            'y': 'result',
            'data': 'input_data',
            'result': 'output_result'
        }

        improved_code = code
        for old_name, new_name in improvements.items():
            # Only replace standalone variables, not parts of words
            pattern = r'\b' + re.escape(old_name) + r'\b'
            improved_code = re.sub(pattern, new_name, improved_code)

        return improved_code

    def _add_type_hints(self, code: str, language: str) -> str:
        """Add type hints to Python functions"""
        if language.lower() != "python":
            return code

        # Basic type hint addition
        lines = code.split('\n')
        typed_lines = []

        for line in lines:
            if line.strip().startswith('def ') and '(' in line and '->' not in line:
                # Add basic return type hint
                if ':' in line:
                    line = line.replace(':', ' -> Any:')
            typed_lines.append(line)

        return '\n'.join(typed_lines)

class EnhancedWebScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.documentation_sources = {
            'python': 'https://docs.python.org/3/',
            'javascript': 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
            'react': 'https://reactjs.org/docs/',
            'node': 'https://nodejs.org/en/docs/',
            'typescript': 'https://www.typescriptlang.org/docs/'
        }

    def enhanced_web_search(self, query: str, context: str = "") -> str:
        """Enhanced web information retrieval with context awareness"""
        try:
            results = []

            # Search multiple sources
            sources = [
                self._search_stackoverflow(query),
                self._search_github(query),
                self._search_documentation(query, context)
            ]

            for source_result in sources:
                if source_result:
                    results.append(source_result)

            if results:
                return f"🌐 Enhanced Web Search Results for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ No relevant information found for '{query}'"

        except Exception as e:
            return f"❌ Error during web search: {str(e)}"

    def _search_stackoverflow(self, query: str) -> str:
        """Search Stack Overflow for solutions"""
        try:
            # Use Stack Exchange API
            api_url = f"https://api.stackexchange.com/2.3/search/advanced"
            params = {
                'order': 'desc',
                'sort': 'relevance',
                'q': query,
                'site': 'stackoverflow',
                'pagesize': 3
            }

            response = self.session.get(api_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('items', []):
                    title = item.get('title', 'No title')
                    link = item.get('link', '')
                    score = item.get('score', 0)
                    results.append(f"📝 {title} (Score: {score})\n   {link}")

                return "🔍 Stack Overflow Results:\n" + "\n".join(results)

        except Exception as e:
            logging.error(f"Stack Overflow search error: {e}")

        return ""

    def _search_github(self, query: str) -> str:
        """Search GitHub for code examples"""
        try:
            # GitHub search API
            api_url = "https://api.github.com/search/repositories"
            params = {
                'q': query,
                'sort': 'stars',
                'order': 'desc',
                'per_page': 3
            }

            response = self.session.get(api_url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('items', []):
                    name = item.get('full_name', 'Unknown')
                    description = item.get('description', 'No description')
                    stars = item.get('stargazers_count', 0)
                    url = item.get('html_url', '')
                    results.append(f"⭐ {name} ({stars} stars)\n   {description}\n   {url}")

                return "🐙 GitHub Results:\n" + "\n".join(results)

        except Exception as e:
            logging.error(f"GitHub search error: {e}")

        return ""

    def _search_documentation(self, query: str, context: str) -> str:
        """Search official documentation"""
        try:
            # Determine language from context
            language = self._detect_language(context)

            if language in self.documentation_sources:
                base_url = self.documentation_sources[language]
                search_url = f"{base_url}search.html?q={urllib.parse.quote(query)}"

                response = self.session.get(search_url, timeout=10)
                if response.status_code == 200:
                    return f"📚 Official {language.title()} Documentation:\n   {search_url}"

        except Exception as e:
            logging.error(f"Documentation search error: {e}")

        return ""

    def _detect_language(self, context: str) -> str:
        """Detect programming language from context"""
        context_lower = context.lower()

        if any(keyword in context_lower for keyword in ['python', 'py', 'pip', 'django', 'flask']):
            return 'python'
        elif any(keyword in context_lower for keyword in ['javascript', 'js', 'node', 'npm']):
            return 'javascript'
        elif any(keyword in context_lower for keyword in ['react', 'jsx', 'component']):
            return 'react'
        elif any(keyword in context_lower for keyword in ['typescript', 'ts']):
            return 'typescript'

        return 'python'  # Default

class GitManager:
    def __init__(self):
        self.git_commands = {
            'status': 'git status --porcelain',
            'add_all': 'git add .',
            'commit': 'git commit -m',
            'push': 'git push',
            'pull': 'git pull',
            'branch': 'git branch',
            'checkout': 'git checkout',
            'merge': 'git merge',
            'log': 'git log --oneline -10'
        }

    def git_operation(self, operation: str, args: str = "") -> str:
        """Perform Git operations"""
        try:
            if operation not in self.git_commands:
                return f"❌ Unknown git operation: {operation}"

            command = self.git_commands[operation]
            if args:
                command += f" {args}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                return f"✅ Git {operation} successful:\n{output}" if output else f"✅ Git {operation} completed"
            else:
                error = result.stderr.strip()
                return f"❌ Git {operation} failed:\n{error}"

        except subprocess.TimeoutExpired:
            return f"⏰ Git {operation} timed out"
        except Exception as e:
            return f"❌ Git operation error: {str(e)}"

    def get_git_status(self) -> Dict:
        """Get comprehensive git status"""
        try:
            status_result = subprocess.run(
                ['git', 'status', '--porcelain'],
                capture_output=True,
                text=True,
                timeout=10
            )

            branch_result = subprocess.run(
                ['git', 'branch', '--show-current'],
                capture_output=True,
                text=True,
                timeout=10
            )

            return {
                'is_git_repo': status_result.returncode == 0,
                'current_branch': branch_result.stdout.strip() if branch_result.returncode == 0 else 'unknown',
                'modified_files': status_result.stdout.strip().split('\n') if status_result.stdout.strip() else [],
                'has_changes': bool(status_result.stdout.strip())
            }

        except Exception as e:
            return {
                'is_git_repo': False,
                'error': str(e)
            }

    def auto_commit_and_push(self, message: str = "Auto-commit by AI Agent") -> str:
        """Automatically commit and push changes"""
        try:
            # Check if there are changes
            status = self.get_git_status()
            if not status.get('has_changes'):
                return "✅ No changes to commit"

            # Add all changes
            add_result = self.git_operation('add_all')
            if '❌' in add_result:
                return add_result

            # Commit changes
            commit_result = self.git_operation('commit', f'"{message}"')
            if '❌' in commit_result:
                return commit_result

            # Push changes
            push_result = self.git_operation('push')
            return push_result

        except Exception as e:
            return f"❌ Auto commit/push error: {str(e)}"

class PackageManager:
    def __init__(self):
        self.managers = {
            'python': {
                'install': 'pip install',
                'uninstall': 'pip uninstall -y',
                'list': 'pip list',
                'update': 'pip install --upgrade',
                'requirements': 'pip freeze > requirements.txt'
            },
            'node': {
                'install': 'npm install',
                'uninstall': 'npm uninstall',
                'list': 'npm list',
                'update': 'npm update',
                'requirements': 'npm init -y'
            },
            'rust': {
                'install': 'cargo add',
                'uninstall': 'cargo remove',
                'list': 'cargo tree',
                'update': 'cargo update',
                'requirements': 'cargo init'
            }
        }

    def detect_project_type(self, directory: str = ".") -> str:
        """Auto-detect project type based on files"""
        files = os.listdir(directory)

        if 'package.json' in files:
            return 'node'
        elif 'requirements.txt' in files or any(f.endswith('.py') for f in files):
            return 'python'
        elif 'Cargo.toml' in files:
            return 'rust'
        elif 'pom.xml' in files:
            return 'java'
        elif 'composer.json' in files:
            return 'php'
        else:
            return 'unknown'

    def install_package(self, package: str, project_type: str = None) -> str:
        """Install a package using appropriate package manager"""
        try:
            if not project_type:
                project_type = self.detect_project_type()

            if project_type not in self.managers:
                return f"❌ Unsupported project type: {project_type}"

            command = f"{self.managers[project_type]['install']} {package}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes for package installation
            )

            if result.returncode == 0:
                return f"✅ Successfully installed {package} using {project_type} package manager"
            else:
                error = result.stderr.strip()
                return f"❌ Failed to install {package}:\n{error}"

        except subprocess.TimeoutExpired:
            return f"⏰ Package installation timed out"
        except Exception as e:
            return f"❌ Package installation error: {str(e)}"

    def manage_dependencies(self, action: str, package: str = "", project_type: str = None) -> str:
        """Manage project dependencies"""
        try:
            if not project_type:
                project_type = self.detect_project_type()

            if project_type not in self.managers:
                return f"❌ Unsupported project type: {project_type}"

            if action not in self.managers[project_type]:
                return f"❌ Unsupported action: {action}"

            command = self.managers[project_type][action]
            if package:
                command += f" {package}"

            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                return f"✅ {action.title()} completed:\n{output}" if output else f"✅ {action.title()} completed"
            else:
                error = result.stderr.strip()
                return f"❌ {action.title()} failed:\n{error}"

        except Exception as e:
            return f"❌ Dependency management error: {str(e)}"

class AdvancedCodeAnalyzer:
    """Advanced code analysis with AST parsing and security checks"""

    def __init__(self):
        self.supported_languages = ['python', 'javascript', 'typescript', 'java', 'cpp', 'rust', 'go']

    def deep_analyze_code(self, code: str, language: str = "python") -> 'CodeAnalysisResult':
        """Perform deep code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = CodeAnalysisResult()

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis.functions.append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis.classes.append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis.imports.append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis.imports.append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis.variables.append(target.id)

                    # Calculate complexity (simplified)
                    analysis.complexity = len(analysis.functions) + len(analysis.classes)

                    # Basic security checks
                    if 'eval' in code or 'exec' in code:
                        analysis.security_issues.append("Use of eval/exec detected")
                    if 'input(' in code and 'int(' not in code:
                        analysis.security_issues.append("Unvalidated user input")

                    return analysis

                except SyntaxError as e:
                    analysis = CodeAnalysisResult()
                    analysis.syntax_errors.append(str(e))
                    return analysis
            else:
                # Basic analysis for other languages
                analysis = CodeAnalysisResult()
                analysis.lines = len(code.split('\n'))
                analysis.characters = len(code)
                return analysis

        except Exception as e:
            analysis = CodeAnalysisResult()
            analysis.syntax_errors.append(f"Analysis error: {str(e)}")
            return analysis

@dataclass
class CodeAnalysisResult:
    """Result of code analysis"""
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    variables: List[str] = field(default_factory=list)
    complexity: int = 0
    security_issues: List[str] = field(default_factory=list)
    syntax_errors: List[str] = field(default_factory=list)
    lines: int = 0
    characters: int = 0

class RefactoringEngine:
    """Advanced code refactoring engine"""

    def __init__(self):
        self.refactor_patterns = {
            'python': {
                'extract_function': r'def\s+(\w+)\([^)]*\):',
                'extract_class': r'class\s+(\w+)(?:\([^)]*\))?:',
                'remove_duplicates': r'(\w+)\s*=\s*(\w+)\s*=\s*(.+)',
            }
        }

    def auto_refactor(self, code: str, language: str = "python") -> str:
        """Automatically refactor code for better structure"""
        try:
            if language.lower() == "python":
                # Simple refactoring suggestions
                suggestions = []

                # Check for long functions
                functions = re.findall(r'def\s+(\w+)\([^)]*\):(.*?)(?=def|\Z)', code, re.DOTALL)
                for func_name, func_body in functions:
                    lines = func_body.strip().split('\n')
                    if len(lines) > 20:
                        suggestions.append(f"Function '{func_name}' is too long ({len(lines)} lines). Consider breaking it down.")

                # Check for code duplication
                lines = code.split('\n')
                line_counts = {}
                for line in lines:
                    stripped = line.strip()
                    if stripped and not stripped.startswith('#'):
                        line_counts[stripped] = line_counts.get(stripped, 0) + 1

                duplicates = [line for line, count in line_counts.items() if count > 1]
                if duplicates:
                    suggestions.append(f"Found {len(duplicates)} duplicate lines that could be refactored.")

                if suggestions:
                    return "🔄 Refactoring Suggestions:\n" + "\n".join([f"• {s}" for s in suggestions])
                else:
                    return "✅ Code structure looks good!"
            else:
                return f"🔄 Basic refactoring analysis for {language} - consider modularizing large functions"

        except Exception as e:
            return f"❌ Refactoring error: {str(e)}"

class LanguageConverter:
    """Cross-language code conversion engine"""

    def __init__(self):
        self.conversion_patterns = {
            'python_to_javascript': {
                'print': 'console.log',
                'def ': 'function ',
                'True': 'true',
                'False': 'false',
                'None': 'null',
            },
            'javascript_to_python': {
                'console.log': 'print',
                'function ': 'def ',
                'true': 'True',
                'false': 'False',
                'null': 'None',
            }
        }

    def convert_code(self, code: str, from_lang: str, to_lang: str) -> str:
        """Convert code between programming languages"""
        try:
            conversion_key = f"{from_lang.lower()}_to_{to_lang.lower()}"

            if conversion_key in self.conversion_patterns:
                converted_code = code
                patterns = self.conversion_patterns[conversion_key]

                for old_pattern, new_pattern in patterns.items():
                    converted_code = converted_code.replace(old_pattern, new_pattern)

                return f"� Converted from {from_lang} to {to_lang}:\n```{to_lang}\n{converted_code}\n```"
            else:
                return f"❌ Conversion from {from_lang} to {to_lang} not yet supported"

        except Exception as e:
            return f"❌ Code conversion error: {str(e)}"

class WebScraper:
    """Enhanced web scraping and information retrieval"""

    def __init__(self):
        self.search_engines = {
            'stackoverflow': 'https://stackoverflow.com/search?q=',
            'github': 'https://github.com/search?q=',
            'docs_python': 'https://docs.python.org/3/search.html?q=',
            'mdn': 'https://developer.mozilla.org/en-US/search?q='
        }

    def enhanced_web_search(self, query: str, context: str = "") -> str:
        """Enhanced web search with multiple sources"""
        try:
            # Simple implementation - in a real scenario, you'd use proper APIs
            results = []

            # Add context to query if available
            if context:
                enhanced_query = f"{query} {context}"
            else:
                enhanced_query = query

            # Simulate search results
            results.append(f"🔍 Search results for: {enhanced_query}")
            results.append("📚 Stack Overflow: Found relevant discussions about error handling")
            results.append("🐙 GitHub: Located example repositories with similar implementations")
            results.append("� Documentation: Official docs with best practices")

            return "\n".join(results)

        except Exception as e:
            return f"❌ Web search error: {str(e)}"

# Main execution
if __name__ == "__main__":
    try:
        print("🚀 Initializing Advanced CLI Coding Agent...")
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully!")
        agent.run_agent()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        print("🔧 Please check your environment and dependencies.")
        print("💡 Try installing missing dependencies: pip install langchain langchain-community langchain-google-genai")


if __name__ == "__main__":
    try:
        print("🚀 Initializing Advanced CLI Coding Agent with OpenCode Integration...")
        print("🔧 Loading AI providers and OpenCode tools...")
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully!")
        print("🎯 OpenCode tools available: view, edit, write, bash, glob, grep, ls, fetch")
        print("🔄 Provider switching available with /switch command")
        print("💡 Type 'help' for comprehensive guide")
        agent.run_agent()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        print("🔧 Please check your environment and dependencies.")
        print("💡 Try installing missing dependencies: pip install langchain langchain-community langchain-google-genai")
• Current Directory: {self.context.current_directory}
• Project Type: {project_type.title()}
• Operating System: {os.name}
• Memory Usage: {memory_percent}%
• CPU Usage: {cpu_percent}%

📁 PROJECT CONTEXT:
• Active Files: {len(self.context.active_files)} files
• Command History: {len(self.context.command_history)} commands
• Working Memory: {len(self.context.working_memory)} items
• Cache Size: {len(self.cache)} cached items
• Conversation Memory: {len(self.memory.buffer)} messages

🔄 GIT STATUS:
• Repository: {'✅ Active' if git_status.get('is_git_repo') else '❌ Not a Git repo'}
• Current Branch: {git_status.get('current_branch', 'N/A')}
• Changes: {'✅ Clean' if not git_status.get('has_changes') else f"⚠️ {len(git_status.get('modified_files', []))} modified files"}

🧠 INTELLIGENCE STATUS:
• Predictive Cache: {'✅ Active' if predictions else '⏸️ Idle'}
• Background Processing: ✅ Running
• Pattern Analysis: ✅ Learning
• Last Error: {self.context.last_error or '✅ None'}

� PREDICTIVE SUGGESTIONS:
{chr(10).join([f"  • {pred}" for pred in predictions]) if predictions else "  • No predictions available"}

📁 RECENT FILES:
{chr(10).join([f"  • {Path(f).name} ({Path(f).suffix})" for f in self.context.active_files[-5:]]) if self.context.active_files else "  • No recent files"}

⚡ RECENT COMMANDS:
{chr(10).join([f"  • {cmd[:50]}{'...' if len(cmd) > 50 else ''}" for cmd in self.context.command_history[-3:]]) if self.context.command_history else "  • No recent commands"}

🎯 CAPABILITIES STATUS:
• Code Analysis: ✅ Ready
• Cross-Language Conversion: ✅ Ready
• Security Auditing: ✅ Ready
• Performance Profiling: ✅ Ready
• Web Research: ✅ Ready
• Package Management: ✅ Ready
• Git Operations: ✅ Ready
• Multi-Step Pipelines: ✅ Ready

💡 QUICK ACTIONS:
• Type 'suggestions' for context-aware recommendations
• Type 'help' for comprehensive capabilities guide
• Type 'pipeline [description]' for automated workflows
""")
        except Exception as e:
            print(f"❌ Error displaying status: {str(e)}")
            print("📊 Basic Status: Agent is running but status details unavailable")

if __name__ == "__main__":
    try:
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully!")
        agent.run_agent()
    except Exception as e:
        print(f"❌ Failed to initialize agent: {str(e)}")
        print("🔧 Please check your environment and dependencies.")